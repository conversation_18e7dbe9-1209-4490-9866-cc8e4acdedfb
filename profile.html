<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate="nav.account">الملف الشخصي - مركز MSB</title>
    <meta name="description" content="الملف الشخصي - إدارة حسابك ومعلوماتك الشخصية">
    <meta name="keywords" content="الملف الشخصي, حسابي, معلومات شخصية, مركز MSB">

    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

    <!-- ملفات الترجمة والوظائف المشتركة -->
    <script src="js/translations.js"></script>
    <script src="js/common.js"></script>

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'pharaoh-gold': '#D4AF37',
                        'pharaoh-blue': '#003366',
                        'pharaoh-sand': '#F4E4BC',
                        'pharaoh-copper': '#B87333',
                    },
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                        'pharaoh': ['Amiri', 'serif'],
                    }
                }
            }
        }
    </script>

    <style>
        .dark {
            background-color: #1a1a1a;
            color: #ffffff;
        }

        .dark .bg-white {
            background-color: #2d2d2d !important;
        }

        .dark .text-gray-600 {
            color: #a0a0a0 !important;
        }

        .dark .text-gray-700 {
            color: #b0b0b0 !important;
        }

        .dark .border-gray-200 {
            border-color: #404040 !important;
        }

        .profile-tab {
            transition: all 0.3s ease;
        }

        .profile-tab.active {
            background-color: #D4AF37;
            color: #003366;
        }

        .profile-section {
            transition: all 0.3s ease;
        }

        .avatar-upload {
            position: relative;
            overflow: hidden;
            display: inline-block;
        }

        .avatar-upload input[type=file] {
            position: absolute;
            left: -9999px;
        }
    </style>
</head>
<body class="font-arabic bg-gray-50">
    <!-- شريط العروض -->
    <div class="bg-pharaoh-gold text-pharaoh-blue py-2 text-center text-sm font-medium animate-pulse">
        <div class="container mx-auto px-4">
            <span data-translate="profile.welcome_message">👤 مرحباً بك في ملفك الشخصي! 👤</span>
        </div>
    </div>

    <!-- الهيدر -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <!-- الهيدر الرئيسي -->
            <div class="flex items-center justify-between h-16">
                <!-- الشعار -->
                <div class="flex items-center space-x-2 space-x-reverse">
                    <div class="w-10 h-10 bg-pharaoh-gold rounded-lg flex items-center justify-center">
                        <span class="text-pharaoh-blue font-bold text-xl">M</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-pharaoh text-pharaoh-blue" data-translate="site.title">مركز MSB</h1>
                        <p class="text-xs text-gray-600" data-translate="site.tagline">One Brand, Every Solution</p>
                    </div>
                </div>

                <!-- شريط البحث -->
                <div class="hidden lg:flex flex-1 max-w-xl mx-8">
                    <div class="relative w-full">
                        <input type="text" data-translate="search.placeholder" placeholder="ابحث عن المنتجات والخدمات..."
                               class="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                        <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            🔍
                        </div>
                    </div>
                </div>

                <!-- أدوات الهيدر -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button id="themeToggle" class="p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="theme.toggle" title="تبديل الثيم">🌙</button>

                    <!-- قائمة اللغات -->
                    <div class="relative">
                        <button id="languageToggle" class="flex items-center p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="language.change" title="تغيير اللغة">
                            <span id="currentFlag">🇪🇬</span>
                            <span class="ml-1 text-sm font-medium" id="currentLang">العربية</span>
                        </button>
                        <div id="languageMenu" class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border hidden z-50">
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors language-option" data-lang="ar" data-flag="🇪🇬" data-name="العربية">
                                <span class="mr-2">🇪🇬</span>
                                <span data-translate="language.arabic">العربية</span>
                                <span class="mr-auto text-green-500" id="check-ar">✓</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors language-option" data-lang="en" data-flag="🇺🇸" data-name="English">
                                <span class="mr-2">🇺🇸</span>
                                <span data-translate="language.english">English</span>
                                <span class="mr-auto text-green-500 hidden" id="check-en">✓</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors language-option" data-lang="fr" data-flag="🇫🇷" data-name="Français">
                                <span class="mr-2">🇫🇷</span>
                                <span data-translate="language.french">Français</span>
                                <span class="mr-auto text-green-500 hidden" id="check-fr">✓</span>
                            </a>
                        </div>
                    </div>

                    <button class="relative p-2 rounded-lg hover:bg-gray-100 transition-colors wishlist-counter" data-translate-title="nav.wishlist" title="المفضلة" onclick="window.location.href='wishlist.html'">
                        ❤️
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-pharaoh-gold text-pharaoh-blue text-xs rounded-full flex items-center justify-center font-bold">3</span>
                    </button>
                    <button class="relative p-2 rounded-lg hover:bg-gray-100 transition-colors cart-counter" data-translate-title="nav.cart" title="سلة التسوق" onclick="window.location.href='cart.html'">
                        🛒
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-pharaoh-gold text-pharaoh-blue text-xs rounded-full flex items-center justify-center font-bold">2</span>
                    </button>

                    <!-- قائمة المستخدم -->
                    <div class="relative">
                        <button id="userToggle" class="p-2 rounded-lg bg-pharaoh-gold text-pharaoh-blue" data-translate-title="nav.account" title="حسابي">👤</button>
                        <div id="userMenu" class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border hidden z-50">
                            <div class="px-4 py-2 border-b border-gray-200">
                                <p class="text-sm font-medium text-gray-900">أحمد محمد</p>
                                <p class="text-xs text-gray-500"><EMAIL></p>
                            </div>
                            <a href="#" class="flex items-center px-4 py-2 bg-pharaoh-gold/20 transition-colors user-option" data-action="profile">
                                <span class="mr-2">👤</span>
                                <span data-translate="nav.account">حسابي</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="orders">
                                <span class="mr-2">📦</span>
                                <span data-translate="nav.orders">طلباتي</span>
                                <span class="mr-auto bg-pharaoh-gold text-pharaoh-blue text-xs px-2 py-1 rounded-full">3</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="wishlist">
                                <span class="mr-2">❤️</span>
                                <span data-translate="nav.wishlist">المفضلة</span>
                                <span class="mr-auto bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">5</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="settings">
                                <span class="mr-2">⚙️</span>
                                <span data-translate="nav.settings">الإعدادات</span>
                            </a>
                            <hr class="my-1">
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-red-50 transition-colors text-red-600 user-option" data-action="logout">
                                <span class="mr-2">🚪</span>
                                <span data-translate="nav.logout">تسجيل الخروج</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التنقل الرئيسي -->
            <nav class="hidden lg:flex items-center justify-center py-4 border-t border-gray-200">
                <div class="flex items-center space-x-8 space-x-reverse">
                    <a href="index.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.home">الرئيسية</a>
                    <a href="products.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.products">المنتجات</a>
                    <a href="services.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.services">الخدمات</a>
                    <a href="about.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.about">من نحن</a>
                    <a href="contact.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.contact">اتصل بنا</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- شريط التنقل -->
    <div class="bg-pharaoh-blue text-white py-3">
        <div class="container mx-auto px-4">
            <div class="flex items-center space-x-2 space-x-reverse text-sm">
                <a href="index.html" class="hover:text-pharaoh-gold" data-translate="nav.home">الرئيسية</a>
                <span>←</span>
                <span class="text-pharaoh-gold" data-translate="nav.account">الملف الشخصي</span>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <main class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-3xl font-pharaoh text-pharaoh-blue mb-2" data-translate="profile.title">الملف الشخصي</h1>
                <p class="text-gray-600" data-translate="profile.subtitle">إدارة معلوماتك الشخصية وإعدادات حسابك</p>
            </div>
            <div class="text-sm text-gray-500">
                <span data-translate="profile.last_login">آخر دخول:</span> <span>اليوم 2:30 م</span>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- شريط التبويب الجانبي -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <!-- صورة المستخدم -->
                    <div class="text-center mb-6">
                        <div class="avatar-upload">
                            <div class="w-24 h-24 bg-pharaoh-gold rounded-full flex items-center justify-center mx-auto mb-4 cursor-pointer" onclick="document.getElementById('avatarInput').click()">
                                <span class="text-3xl text-pharaoh-blue">👤</span>
                            </div>
                            <input type="file" id="avatarInput" accept="image/*" onchange="uploadAvatar(this)">
                        </div>
                        <h3 class="text-lg font-bold text-pharaoh-blue">أحمد محمد</h3>
                        <p class="text-gray-600 text-sm">عضو ذهبي</p>
                        <div class="flex items-center justify-center mt-2">
                            <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                            <span class="text-sm text-gray-600 mr-2">(4.8)</span>
                        </div>
                    </div>

                    <!-- قائمة التبويب -->
                    <nav class="space-y-2">
                        <button onclick="showTab('personal')" class="profile-tab active w-full text-right px-4 py-3 rounded-lg transition-colors flex items-center" data-translate="profile.tab_personal">
                            <span class="mr-3">👤</span>
                            المعلومات الشخصية
                        </button>
                        <button onclick="showTab('security')" class="profile-tab w-full text-right px-4 py-3 rounded-lg hover:bg-gray-100 transition-colors flex items-center" data-translate="profile.tab_security">
                            <span class="mr-3">🔒</span>
                            الأمان والخصوصية
                        </button>
                        <button onclick="showTab('addresses')" class="profile-tab w-full text-right px-4 py-3 rounded-lg hover:bg-gray-100 transition-colors flex items-center" data-translate="profile.tab_addresses">
                            <span class="mr-3">📍</span>
                            العناوين
                        </button>
                        <button onclick="showTab('preferences')" class="profile-tab w-full text-right px-4 py-3 rounded-lg hover:bg-gray-100 transition-colors flex items-center" data-translate="profile.tab_preferences">
                            <span class="mr-3">⚙️</span>
                            التفضيلات
                        </button>
                        <button onclick="showTab('notifications')" class="profile-tab w-full text-right px-4 py-3 rounded-lg hover:bg-gray-100 transition-colors flex items-center" data-translate="profile.tab_notifications">
                            <span class="mr-3">🔔</span>
                            الإشعارات
                        </button>
                    </nav>

                    <!-- إحصائيات سريعة -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="text-sm font-bold text-gray-700 mb-4" data-translate="profile.quick_stats">إحصائيات سريعة</h4>
                        <div class="space-y-3">
                            <div class="flex justify-between text-sm">
                                <span data-translate="profile.total_orders">إجمالي الطلبات</span>
                                <span class="font-bold text-pharaoh-blue">24</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span data-translate="profile.total_spent">إجمالي المشتريات</span>
                                <span class="font-bold text-pharaoh-blue">45,670 ج.م</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span data-translate="profile.loyalty_points">نقاط الولاء</span>
                                <span class="font-bold text-pharaoh-gold">1,250</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- محتوى التبويب -->
            <div class="lg:col-span-3">
                <!-- تبويب المعلومات الشخصية -->
                <div id="personal-tab" class="profile-section bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-2xl font-bold text-pharaoh-blue mb-6" data-translate="profile.personal_info">المعلومات الشخصية</h2>

                    <form id="personalForm" onsubmit="updatePersonalInfo(event)">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.first_name">الاسم الأول</label>
                                <input type="text" name="firstName" value="أحمد" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.last_name">الاسم الأخير</label>
                                <input type="text" name="lastName" value="محمد" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.email">البريد الإلكتروني</label>
                                <input type="email" name="email" value="<EMAIL>" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.phone">رقم الهاتف</label>
                                <input type="tel" name="phone" value="+20 1234567890" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.birth_date">تاريخ الميلاد</label>
                                <input type="date" name="birthDate" value="1990-01-15"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.gender">الجنس</label>
                                <select name="gender"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                                    <option value="male" selected data-translate="form.male">ذكر</option>
                                    <option value="female" data-translate="form.female">أنثى</option>
                                    <option value="other" data-translate="form.other">آخر</option>
                                </select>
                            </div>
                        </div>

                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.bio">نبذة شخصية</label>
                            <textarea name="bio" rows="4"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent"
                                      placeholder="اكتب نبذة مختصرة عنك...">مطور ومصمم مواقع ويب، أحب التكنولوجيا والتسوق الإلكتروني.</textarea>
                        </div>

                        <div class="flex justify-end mt-8">
                            <button type="submit"
                                    class="bg-pharaoh-blue text-white px-8 py-3 rounded-lg font-bold hover:bg-blue-800 transition-colors" data-translate="btn.save_changes">
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>

                <!-- تبويب الأمان والخصوصية -->
                <div id="security-tab" class="profile-section bg-white rounded-lg shadow-lg p-6 hidden">
                    <h2 class="text-2xl font-bold text-pharaoh-blue mb-6" data-translate="profile.security_privacy">الأمان والخصوصية</h2>

                    <!-- تغيير كلمة المرور -->
                    <div class="mb-8">
                        <h3 class="text-lg font-bold text-gray-800 mb-4" data-translate="profile.change_password">تغيير كلمة المرور</h3>
                        <form id="passwordForm" onsubmit="changePassword(event)">
                            <div class="grid grid-cols-1 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.current_password">كلمة المرور الحالية</label>
                                    <input type="password" name="currentPassword" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.new_password">كلمة المرور الجديدة</label>
                                    <input type="password" name="newPassword" required minlength="8"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.confirm_password">تأكيد كلمة المرور</label>
                                    <input type="password" name="confirmPassword" required minlength="8"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                                </div>
                            </div>
                            <button type="submit"
                                    class="mt-4 bg-pharaoh-blue text-white px-6 py-2 rounded-lg hover:bg-blue-800 transition-colors" data-translate="btn.change_password">
                                تغيير كلمة المرور
                            </button>
                        </form>
                    </div>

                    <!-- المصادقة الثنائية -->
                    <div class="mb-8">
                        <h3 class="text-lg font-bold text-gray-800 mb-4" data-translate="profile.two_factor">المصادقة الثنائية</h3>
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <p class="font-medium" data-translate="profile.two_factor_desc">تأمين إضافي لحسابك</p>
                                <p class="text-sm text-gray-600" data-translate="profile.two_factor_info">احم حسابك بطبقة أمان إضافية</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" class="sr-only peer" onchange="toggleTwoFactor(this)">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-pharaoh-gold/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pharaoh-gold"></div>
                            </label>
                        </div>
                    </div>

                    <!-- جلسات النشاط -->
                    <div class="mb-8">
                        <h3 class="text-lg font-bold text-gray-800 mb-4" data-translate="profile.active_sessions">الجلسات النشطة</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div class="flex items-center">
                                    <span class="text-2xl mr-3">💻</span>
                                    <div>
                                        <p class="font-medium">Chrome على Windows</p>
                                        <p class="text-sm text-gray-600">القاهرة، مصر - الآن</p>
                                    </div>
                                </div>
                                <span class="text-green-600 text-sm font-medium" data-translate="profile.current_session">الجلسة الحالية</span>
                            </div>
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div class="flex items-center">
                                    <span class="text-2xl mr-3">📱</span>
                                    <div>
                                        <p class="font-medium">Safari على iPhone</p>
                                        <p class="text-sm text-gray-600">القاهرة، مصر - منذ ساعتين</p>
                                    </div>
                                </div>
                                <button class="text-red-600 text-sm hover:text-red-800" onclick="terminateSession('mobile')" data-translate="btn.terminate">
                                    إنهاء
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الخصوصية -->
                    <div>
                        <h3 class="text-lg font-bold text-gray-800 mb-4" data-translate="profile.privacy_settings">إعدادات الخصوصية</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium" data-translate="profile.profile_visibility">إظهار الملف الشخصي</p>
                                    <p class="text-sm text-gray-600" data-translate="profile.profile_visibility_desc">السماح للآخرين برؤية ملفك الشخصي</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-pharaoh-gold/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pharaoh-gold"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium" data-translate="profile.order_history_privacy">خصوصية تاريخ الطلبات</p>
                                    <p class="text-sm text-gray-600" data-translate="profile.order_history_privacy_desc">إخفاء تاريخ طلباتك عن الآخرين</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-pharaoh-gold/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pharaoh-gold"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب العناوين -->
                <div id="addresses-tab" class="profile-section bg-white rounded-lg shadow-lg p-6 hidden">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-pharaoh-blue" data-translate="profile.addresses">العناوين</h2>
                        <button onclick="addNewAddress()" class="bg-pharaoh-gold text-pharaoh-blue px-4 py-2 rounded-lg hover:bg-yellow-300 transition-colors" data-translate="btn.add_address">
                            إضافة عنوان جديد
                        </button>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- العنوان الرئيسي -->
                        <div class="border border-pharaoh-gold bg-pharaoh-gold/10 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="font-bold text-lg" data-translate="profile.primary_address">العنوان الرئيسي</h3>
                                <span class="bg-pharaoh-gold text-pharaoh-blue px-2 py-1 rounded text-xs font-bold" data-translate="profile.default">افتراضي</span>
                            </div>
                            <div class="space-y-2 text-gray-700">
                                <p class="font-medium">أحمد محمد</p>
                                <p>شارع التحرير، وسط البلد</p>
                                <p>القاهرة، مصر 11511</p>
                                <p>+20 1234567890</p>
                            </div>
                            <div class="flex space-x-2 space-x-reverse mt-4">
                                <button onclick="editAddress('primary')" class="text-pharaoh-blue hover:text-blue-800 text-sm" data-translate="btn.edit">
                                    تعديل
                                </button>
                                <button onclick="deleteAddress('primary')" class="text-red-600 hover:text-red-800 text-sm" data-translate="btn.delete">
                                    حذف
                                </button>
                            </div>
                        </div>

                        <!-- عنوان العمل -->
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="font-bold text-lg" data-translate="profile.work_address">عنوان العمل</h3>
                                <span class="bg-gray-200 text-gray-700 px-2 py-1 rounded text-xs" data-translate="profile.secondary">ثانوي</span>
                            </div>
                            <div class="space-y-2 text-gray-700">
                                <p class="font-medium">أحمد محمد</p>
                                <p>شارع الجامعة، المهندسين</p>
                                <p>الجيزة، مصر 12411</p>
                                <p>+20 1234567890</p>
                            </div>
                            <div class="flex space-x-2 space-x-reverse mt-4">
                                <button onclick="editAddress('work')" class="text-pharaoh-blue hover:text-blue-800 text-sm" data-translate="btn.edit">
                                    تعديل
                                </button>
                                <button onclick="setAsDefault('work')" class="text-green-600 hover:text-green-800 text-sm" data-translate="btn.set_default">
                                    جعل افتراضي
                                </button>
                                <button onclick="deleteAddress('work')" class="text-red-600 hover:text-red-800 text-sm" data-translate="btn.delete">
                                    حذف
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج إضافة عنوان جديد (مخفي افتراضياً) -->
                    <div id="newAddressForm" class="hidden mt-8 p-6 bg-gray-50 rounded-lg">
                        <h3 class="text-lg font-bold text-gray-800 mb-4" data-translate="profile.add_new_address">إضافة عنوان جديد</h3>
                        <form onsubmit="saveNewAddress(event)">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.address_title">عنوان العنوان</label>
                                    <input type="text" name="addressTitle" required placeholder="مثل: المنزل، العمل، الأصدقاء"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.recipient_name">اسم المستلم</label>
                                    <input type="text" name="recipientName" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.street_address">عنوان الشارع</label>
                                    <input type="text" name="streetAddress" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.city">المدينة</label>
                                    <input type="text" name="city" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.governorate">المحافظة</label>
                                    <select name="governorate" required
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                                        <option value="">اختر المحافظة</option>
                                        <option value="cairo">القاهرة</option>
                                        <option value="giza">الجيزة</option>
                                        <option value="alexandria">الإسكندرية</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.postal_code">الرمز البريدي</label>
                                    <input type="text" name="postalCode"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.phone">رقم الهاتف</label>
                                    <input type="tel" name="phone" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                                </div>
                            </div>
                            <div class="flex items-center mt-6">
                                <input type="checkbox" name="setAsDefault" class="rounded border-gray-300 text-pharaoh-gold focus:ring-pharaoh-gold">
                                <span class="mr-2 text-sm text-gray-700" data-translate="form.set_as_default">جعل هذا العنوان افتراضي</span>
                            </div>
                            <div class="flex space-x-4 space-x-reverse mt-6">
                                <button type="submit"
                                        class="bg-pharaoh-blue text-white px-6 py-2 rounded-lg hover:bg-blue-800 transition-colors" data-translate="btn.save_address">
                                    حفظ العنوان
                                </button>
                                <button type="button" onclick="cancelAddAddress()"
                                        class="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors" data-translate="btn.cancel">
                                    إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // إظهار التبويب المحدد
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.profile-section').forEach(section => {
                section.classList.add('hidden');
            });

            // إزالة الحالة النشطة من جميع الأزرار
            document.querySelectorAll('.profile-tab').forEach(tab => {
                tab.classList.remove('active');
                tab.classList.add('hover:bg-gray-100');
            });

            // إظهار التبويب المحدد
            document.getElementById(tabName + '-tab').classList.remove('hidden');

            // تفعيل الزر المحدد
            event.target.classList.add('active');
            event.target.classList.remove('hover:bg-gray-100');
        }

        // رفع صورة الملف الشخصي
        function uploadAvatar(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // هنا يمكن إضافة منطق رفع الصورة
                    showNotification(t('profile.avatar_updated', 'تم تحديث صورة الملف الشخصي'), 'success');
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // تحديث المعلومات الشخصية
        function updatePersonalInfo(event) {
            event.preventDefault();

            const formData = new FormData(event.target);

            // إظهار رسالة التحميل
            showNotification(t('msg.saving'), 'info');

            // محاكاة حفظ البيانات
            setTimeout(() => {
                showNotification(t('profile.info_updated', 'تم تحديث المعلومات الشخصية بنجاح'), 'success');
            }, 1500);
        }

        // تغيير كلمة المرور
        function changePassword(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const newPassword = formData.get('newPassword');
            const confirmPassword = formData.get('confirmPassword');

            if (newPassword !== confirmPassword) {
                showNotification(t('profile.password_mismatch', 'كلمات المرور غير متطابقة'), 'error');
                return;
            }

            // إظهار رسالة التحميل
            showNotification(t('msg.saving'), 'info');

            // محاكاة تغيير كلمة المرور
            setTimeout(() => {
                showNotification(t('profile.password_changed', 'تم تغيير كلمة المرور بنجاح'), 'success');
                event.target.reset();
            }, 2000);
        }

        // تفعيل/إلغاء المصادقة الثنائية
        function toggleTwoFactor(checkbox) {
            const isEnabled = checkbox.checked;

            if (isEnabled) {
                showNotification(t('profile.two_factor_enabled', 'تم تفعيل المصادقة الثنائية'), 'success');
            } else {
                if (confirm(t('profile.confirm_disable_2fa', 'هل أنت متأكد من إلغاء المصادقة الثنائية؟'))) {
                    showNotification(t('profile.two_factor_disabled', 'تم إلغاء المصادقة الثنائية'), 'info');
                } else {
                    checkbox.checked = true;
                }
            }
        }

        // إنهاء جلسة
        function terminateSession(sessionId) {
            if (confirm(t('profile.confirm_terminate_session', 'هل تريد إنهاء هذه الجلسة؟'))) {
                showNotification(t('profile.session_terminated', 'تم إنهاء الجلسة'), 'success');
                // هنا يمكن إضافة منطق إنهاء الجلسة
            }
        }

        // إضافة عنوان جديد
        function addNewAddress() {
            document.getElementById('newAddressForm').classList.remove('hidden');
            document.getElementById('newAddressForm').scrollIntoView({ behavior: 'smooth' });
        }

        // إلغاء إضافة عنوان
        function cancelAddAddress() {
            document.getElementById('newAddressForm').classList.add('hidden');
            document.getElementById('newAddressForm').querySelector('form').reset();
        }

        // حفظ عنوان جديد
        function saveNewAddress(event) {
            event.preventDefault();

            const formData = new FormData(event.target);

            // إظهار رسالة التحميل
            showNotification(t('msg.saving'), 'info');

            // محاكاة حفظ العنوان
            setTimeout(() => {
                showNotification(t('profile.address_saved', 'تم حفظ العنوان الجديد'), 'success');
                cancelAddAddress();
                // هنا يمكن إضافة العنوان الجديد للقائمة
            }, 1500);
        }

        // تعديل عنوان
        function editAddress(addressId) {
            showNotification(t('profile.edit_address', 'فتح نموذج تعديل العنوان'), 'info');
            // هنا يمكن إضافة منطق تعديل العنوان
        }

        // حذف عنوان
        function deleteAddress(addressId) {
            if (confirm(t('profile.confirm_delete_address', 'هل أنت متأكد من حذف هذا العنوان؟'))) {
                showNotification(t('profile.address_deleted', 'تم حذف العنوان'), 'success');
                // هنا يمكن إضافة منطق حذف العنوان
            }
        }

        // جعل عنوان افتراضي
        function setAsDefault(addressId) {
            showNotification(t('profile.address_set_default', 'تم تعيين العنوان كافتراضي'), 'success');
            // هنا يمكن إضافة منطق تعيين العنوان الافتراضي
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // إظهار التبويب الأول افتراضياً
            showTab('personal');

            // إضافة مستمعي الأحداث للتبويبات
            document.querySelectorAll('.profile-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabName = this.getAttribute('onclick').match(/'([^']+)'/)[1];
                    showTab(tabName);
                });
            });
        });
    </script>
</body>
</html>
