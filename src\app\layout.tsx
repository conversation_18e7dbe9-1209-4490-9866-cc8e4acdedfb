import type { Metadata } from 'next'
import './globals.css'



export const metadata: Metadata = {
  title: 'مركز MSB - One Brand, Every Solution',
  description: 'مركز MSB المصري للتجارة الإلكترونية والخدمات المتكاملة. كل ما تحتاجه تحت سقف واحد.',
  keywords: 'مركز MSB, التجارة الإلكترونية, مصر, منتجات, خدمات, صيانة',
  authors: [{ name: 'MSB Center' }],
  creator: 'MSB Center',
  publisher: 'MSB Center',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://msb-center.com'),
  alternates: {
    canonical: '/',
    languages: {
      'ar': '/ar',
      'en': '/en',
      'fr': '/fr',
    },
  },
  openGraph: {
    title: 'مركز MSB - One Brand, Every Solution',
    description: 'مركز MSB المصري للتجارة الإلكترونية والخدمات المتكاملة',
    url: 'https://msb-center.com',
    siteName: 'MSB Center',
    locale: 'ar_EG',
    type: 'website',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'مركز MSB',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'مركز MSB - One Brand, Every Solution',
    description: 'مركز MSB المصري للتجارة الإلكترونية والخدمات المتكاملة',
    images: ['/twitter-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#003366" />
        <meta name="msapplication-TileColor" content="#003366" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body className="font-arabic antialiased" suppressHydrationWarning>
        <div className="relative min-h-screen bg-background">
          {children}
        </div>
      </body>
    </html>
  )
}
