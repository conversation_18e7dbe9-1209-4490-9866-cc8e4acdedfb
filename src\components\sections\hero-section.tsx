'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronLeft, ChevronRight, ShoppingBag, Wrench, Star, Play } from 'lucide-react'
import { useLanguage } from '@/hooks/use-language'

export default function HeroSection() {
  const { t, isRTL } = useLanguage()
  const [currentSlide, setCurrentSlide] = useState(0)

  const slides = [
    {
      id: 1,
      title: 'مرحباً بك في مركز MSB',
      subtitle: 'أفضل المنتجات والخدمات في مصر',
      description: 'اكتشف مجموعة واسعة من المنتجات عالية الجودة والخدمات المتميزة تحت سقف واحد',
      image: '/hero-slide-1.jpg',
      cta1: { text: 'تسوق الآن', href: '/products', icon: ShoppingBag },
      cta2: { text: 'اطلب خدمة', href: '/services', icon: Wrench },
      badge: 'خصم 50%',
      color: 'from-pharaoh-blue to-pharaoh-gold'
    },
    {
      id: 2,
      title: 'خدمات الصيانة المتخصصة',
      subtitle: 'فريق من الخبراء في خدمتك',
      description: 'نقدم خدمات صيانة احترافية لجميع أنواع الأجهزة مع ضمان الجودة والسرعة',
      image: '/hero-slide-2.jpg',
      cta1: { text: 'احجز موعد', href: '/services/booking', icon: Wrench },
      cta2: { text: 'تعرف أكثر', href: '/about', icon: Play },
      badge: 'ضمان سنة',
      color: 'from-green-600 to-blue-600'
    },
    {
      id: 3,
      title: 'منتجات أصلية بأفضل الأسعار',
      subtitle: 'جودة مضمونة وأسعار تنافسية',
      description: 'تسوق من مجموعة كبيرة من المنتجات الأصلية مع ضمان الجودة وخدمة التوصيل السريع',
      image: '/hero-slide-3.jpg',
      cta1: { text: 'استكشف المنتجات', href: '/products', icon: ShoppingBag },
      cta2: { text: 'العروض الخاصة', href: '/offers', icon: Star },
      badge: 'توصيل مجاني',
      color: 'from-purple-600 to-pink-600'
    }
  ]

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 5000)
    return () => clearInterval(timer)
  }, [slides.length])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
  }

  return (
    <section className="relative h-[600px] md:h-[700px] overflow-hidden">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
          className={`absolute inset-0 bg-gradient-to-r ${slides[currentSlide].color}`}
        >
          {/* الخلفية المزخرفة */}
          <div className="absolute inset-0 hieroglyph-pattern opacity-10"></div>
          
          {/* المحتوى */}
          <div className="relative h-full flex items-center">
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                {/* النص */}
                <motion.div
                  initial={{ opacity: 0, x: isRTL ? 50 : -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2, duration: 0.6 }}
                  className="text-white space-y-6"
                >
                  {/* الشارة */}
                  <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="inline-block"
                  >
                    <span className="bg-pharaoh-gold text-pharaoh-blue px-4 py-2 rounded-full text-sm font-bold">
                      {slides[currentSlide].badge}
                    </span>
                  </motion.div>

                  {/* العنوان */}
                  <motion.h1
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="text-4xl md:text-5xl lg:text-6xl font-pharaoh leading-tight"
                  >
                    {slides[currentSlide].title}
                  </motion.h1>

                  {/* العنوان الفرعي */}
                  <motion.h2
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className="text-xl md:text-2xl font-medium text-white/90"
                  >
                    {slides[currentSlide].subtitle}
                  </motion.h2>

                  {/* الوصف */}
                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    className="text-lg text-white/80 leading-relaxed max-w-lg"
                  >
                    {slides[currentSlide].description}
                  </motion.p>

                  {/* أزرار العمل */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7 }}
                    className="flex flex-col sm:flex-row gap-4"
                  >
                    <motion.a
                      href={slides[currentSlide].cta1.href}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="inline-flex items-center justify-center space-x-2 rtl:space-x-reverse bg-pharaoh-gold text-pharaoh-blue px-8 py-4 rounded-lg font-bold text-lg hover:bg-pharaoh-gold/90 transition-all duration-300 shadow-lg hover:shadow-xl"
                    >
                      <slides[currentSlide].cta1.icon className="w-5 h-5" />
                      <span>{slides[currentSlide].cta1.text}</span>
                    </motion.a>

                    <motion.a
                      href={slides[currentSlide].cta2.href}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="inline-flex items-center justify-center space-x-2 rtl:space-x-reverse bg-white/20 backdrop-blur-sm text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-white/30 transition-all duration-300 border border-white/30"
                    >
                      <slides[currentSlide].cta2.icon className="w-5 h-5" />
                      <span>{slides[currentSlide].cta2.text}</span>
                    </motion.a>
                  </motion.div>

                  {/* إحصائيات سريعة */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 }}
                    className="flex items-center space-x-8 rtl:space-x-reverse pt-6"
                  >
                    <div className="text-center">
                      <div className="text-2xl font-bold text-pharaoh-gold">1000+</div>
                      <div className="text-sm text-white/70">منتج</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-pharaoh-gold">27</div>
                      <div className="text-sm text-white/70">محافظة</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-pharaoh-gold">24/7</div>
                      <div className="text-sm text-white/70">دعم</div>
                    </div>
                  </motion.div>
                </motion.div>

                {/* الصورة */}
                <motion.div
                  initial={{ opacity: 0, x: isRTL ? -50 : 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3, duration: 0.6 }}
                  className="relative hidden lg:block"
                >
                  <div className="relative w-full h-96 rounded-2xl overflow-hidden shadow-2xl">
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent z-10"></div>
                    <div className="w-full h-full bg-white/10 backdrop-blur-sm rounded-2xl flex items-center justify-center">
                      <div className="text-center text-white">
                        <div className="w-24 h-24 bg-pharaoh-gold rounded-full flex items-center justify-center mx-auto mb-4">
                          <slides[currentSlide].cta1.icon className="w-12 h-12 text-pharaoh-blue" />
                        </div>
                        <h3 className="text-xl font-bold mb-2">صورة توضيحية</h3>
                        <p className="text-white/80">سيتم إضافة الصور الفعلية</p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* أزرار التنقل */}
      <button
        onClick={prevSlide}
        className="absolute left-4 rtl:left-auto rtl:right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300 z-20"
      >
        {isRTL ? <ChevronRight className="w-6 h-6" /> : <ChevronLeft className="w-6 h-6" />}
      </button>

      <button
        onClick={nextSlide}
        className="absolute right-4 rtl:right-auto rtl:left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300 z-20"
      >
        {isRTL ? <ChevronLeft className="w-6 h-6" /> : <ChevronRight className="w-6 h-6" />}
      </button>

      {/* مؤشرات الشرائح */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2 rtl:space-x-reverse z-20">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide 
                ? 'bg-pharaoh-gold scale-125' 
                : 'bg-white/50 hover:bg-white/70'
            }`}
          />
        ))}
      </div>

      {/* تأثير الجسيمات */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-pharaoh-gold rounded-full opacity-30"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 0.7, 0.3],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>
    </section>
  )
}
