<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate="nav.contact">اتصل بنا - مركز MSB</title>
    <meta name="description" content="تواصل مع مركز MSB - معلومات الاتصال، العنوان، ونموذج التواصل">
    <meta name="keywords" content="اتصل بنا, تواصل, مركز MSB, خدمة العملاء">

    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

    <!-- ملفات الترجمة والوظائف المشتركة -->
    <script src="js/translations.js"></script>
    <script src="js/common.js"></script>

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'pharaoh-gold': '#D4AF37',
                        'pharaoh-blue': '#003366',
                        'pharaoh-sand': '#F4E4BC',
                        'pharaoh-copper': '#B87333',
                    },
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                        'pharaoh': ['Amiri', 'serif'],
                    },
                    animation: {
                        'float': 'float 3s ease-in-out infinite',
                        'pulse-slow': 'pulse 3s ease-in-out infinite',
                    }
                }
            }
        }
    </script>

    <style>
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .hieroglyph-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E");
        }

        .contact-card {
            transition: all 0.3s ease;
        }

        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .dark {
            background-color: #1a1a1a;
            color: #ffffff;
        }

        .dark .bg-white {
            background-color: #2d2d2d !important;
        }

        .dark .text-gray-600 {
            color: #a0a0a0 !important;
        }

        .dark .text-gray-700 {
            color: #b0b0b0 !important;
        }

        .dark .border-gray-200 {
            border-color: #404040 !important;
        }
    </style>
</head>
<body class="font-arabic">
    <!-- شريط العروض -->
    <div class="bg-pharaoh-gold text-pharaoh-blue py-2 text-center text-sm font-medium animate-pulse">
        <div class="container mx-auto px-4">
            <span data-translate="contact.welcome_message">📞 نحن هنا لخدمتك - تواصل معنا الآن! 📞</span>
        </div>
    </div>

    <!-- الهيدر -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <!-- الهيدر الرئيسي -->
            <div class="flex items-center justify-between h-16">
                <!-- الشعار -->
                <div class="flex items-center space-x-2 space-x-reverse">
                    <div class="w-10 h-10 bg-pharaoh-gold rounded-lg flex items-center justify-center">
                        <span class="text-pharaoh-blue font-bold text-xl">M</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-pharaoh text-pharaoh-blue" data-translate="site.title">مركز MSB</h1>
                        <p class="text-xs text-gray-600" data-translate="site.tagline">One Brand, Every Solution</p>
                    </div>
                </div>

                <!-- شريط البحث -->
                <div class="hidden lg:flex flex-1 max-w-xl mx-8">
                    <div class="relative w-full">
                        <input type="text" data-translate="search.placeholder" placeholder="ابحث عن المنتجات والخدمات..."
                               class="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                        <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            🔍
                        </div>
                    </div>
                </div>

                <!-- أدوات الهيدر -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button id="themeToggle" class="p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="theme.toggle" title="تبديل الثيم">🌙</button>

                    <!-- قائمة اللغات -->
                    <div class="relative">
                        <button id="languageToggle" class="flex items-center p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="language.change" title="تغيير اللغة">
                            <span id="currentFlag">🇪🇬</span>
                            <span class="ml-1 text-sm font-medium" id="currentLang">العربية</span>
                        </button>
                        <div id="languageMenu" class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border hidden z-50">
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors language-option" data-lang="ar" data-flag="🇪🇬" data-name="العربية">
                                <span class="mr-2">🇪🇬</span>
                                <span data-translate="language.arabic">العربية</span>
                                <span class="mr-auto text-green-500" id="check-ar">✓</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors language-option" data-lang="en" data-flag="🇺🇸" data-name="English">
                                <span class="mr-2">🇺🇸</span>
                                <span data-translate="language.english">English</span>
                                <span class="mr-auto text-green-500 hidden" id="check-en">✓</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors language-option" data-lang="fr" data-flag="🇫🇷" data-name="Français">
                                <span class="mr-2">🇫🇷</span>
                                <span data-translate="language.french">Français</span>
                                <span class="mr-auto text-green-500 hidden" id="check-fr">✓</span>
                            </a>
                        </div>
                    </div>

                    <button class="relative p-2 rounded-lg hover:bg-gray-100 transition-colors wishlist-counter" data-translate-title="nav.wishlist" title="المفضلة" onclick="window.location.href='wishlist.html'">
                        ❤️
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-pharaoh-gold text-pharaoh-blue text-xs rounded-full flex items-center justify-center font-bold">3</span>
                    </button>
                    <button class="relative p-2 rounded-lg hover:bg-gray-100 transition-colors cart-counter" data-translate-title="nav.cart" title="سلة التسوق" onclick="window.location.href='cart.html'">
                        🛒
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-pharaoh-gold text-pharaoh-blue text-xs rounded-full flex items-center justify-center font-bold">2</span>
                    </button>

                    <!-- قائمة المستخدم -->
                    <div class="relative">
                        <button id="userToggle" class="p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="nav.account" title="حسابي">👤</button>
                        <div id="userMenu" class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border hidden z-50">
                            <div class="px-4 py-2 border-b border-gray-200">
                                <p class="text-sm font-medium text-gray-900">أحمد محمد</p>
                                <p class="text-xs text-gray-500"><EMAIL></p>
                            </div>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="profile">
                                <span class="mr-2">👤</span>
                                <span data-translate="nav.account">حسابي</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="orders">
                                <span class="mr-2">📦</span>
                                <span data-translate="nav.orders">طلباتي</span>
                                <span class="mr-auto bg-pharaoh-gold text-pharaoh-blue text-xs px-2 py-1 rounded-full">3</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="wishlist">
                                <span class="mr-2">❤️</span>
                                <span data-translate="nav.wishlist">المفضلة</span>
                                <span class="mr-auto bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">5</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="settings">
                                <span class="mr-2">⚙️</span>
                                <span data-translate="nav.settings">الإعدادات</span>
                            </a>
                            <hr class="my-1">
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-red-50 transition-colors text-red-600 user-option" data-action="logout">
                                <span class="mr-2">🚪</span>
                                <span data-translate="nav.logout">تسجيل الخروج</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التنقل الرئيسي -->
            <nav class="hidden lg:flex items-center justify-center py-4 border-t border-gray-200">
                <div class="flex items-center space-x-8 space-x-reverse">
                    <a href="index.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.home">الرئيسية</a>
                    <a href="products.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.products">المنتجات</a>
                    <a href="services.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.services">الخدمات</a>
                    <a href="about.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.about">من نحن</a>
                    <a href="contact.html" class="text-pharaoh-blue font-bold transition-colors" data-translate="nav.contact">اتصل بنا</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- شريط التنقل -->
    <div class="bg-pharaoh-blue text-white py-3">
        <div class="container mx-auto px-4">
            <div class="flex items-center space-x-2 space-x-reverse text-sm">
                <a href="index.html" class="hover:text-pharaoh-gold" data-translate="nav.home">الرئيسية</a>
                <span>←</span>
                <span class="text-pharaoh-gold" data-translate="nav.contact">اتصل بنا</span>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <main class="min-h-screen">
        <!-- قسم البطل -->
        <section class="relative h-80 bg-gradient-to-r from-pharaoh-blue to-pharaoh-gold text-white overflow-hidden">
            <div class="absolute inset-0 hieroglyph-pattern opacity-10"></div>
            <div class="relative h-full flex items-center">
                <div class="container mx-auto px-4">
                    <div class="text-center space-y-6">
                        <div class="inline-block bg-pharaoh-gold text-pharaoh-blue px-4 py-2 rounded-full text-sm font-bold animate-pulse">
                            <span data-translate="contact.always_here">نحن هنا دائماً</span>
                        </div>
                        <h1 class="text-4xl md:text-5xl font-pharaoh leading-tight" data-translate="contact.title">
                            تواصل معنا
                        </h1>
                        <p class="text-xl text-white/90 max-w-2xl mx-auto" data-translate="contact.subtitle">
                            فريق خدمة العملاء لدينا جاهز لمساعدتك في أي وقت. تواصل معنا بالطريقة التي تناسبك
                        </p>
                        <div class="flex items-center justify-center space-x-8 space-x-reverse pt-6">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-pharaoh-gold">24/7</div>
                                <div class="text-sm text-white/70" data-translate="contact.support_hours">ساعات الدعم</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-pharaoh-gold">&lt;1h</div>
                                <div class="text-sm text-white/70" data-translate="contact.response_time">وقت الاستجابة</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-pharaoh-gold">99%</div>
                                <div class="text-sm text-white/70" data-translate="contact.satisfaction">رضا العملاء</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم معلومات الاتصال -->
        <section class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-pharaoh text-pharaoh-blue mb-4" data-translate="contact.get_in_touch">
                        تواصل معنا
                    </h2>
                    <p class="text-gray-600 max-w-2xl mx-auto" data-translate="contact.get_in_touch_desc">
                        اختر الطريقة الأنسب للتواصل معنا. نحن متاحون على مدار الساعة لخدمتك
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- الهاتف -->
                    <div class="contact-card bg-white rounded-lg shadow-lg p-6 text-center">
                        <div class="w-16 h-16 bg-pharaoh-gold rounded-full flex items-center justify-center mx-auto mb-4 animate-float">
                            <span class="text-2xl text-pharaoh-blue">📞</span>
                        </div>
                        <h3 class="text-lg font-bold text-pharaoh-blue mb-2" data-translate="contact.phone">
                            الهاتف
                        </h3>
                        <p class="text-gray-600 text-sm mb-4" data-translate="contact.phone_desc">
                            اتصل بنا مباشرة للحصول على المساعدة الفورية
                        </p>
                        <p class="text-pharaoh-gold font-bold text-lg">19999</p>
                        <p class="text-gray-500 text-sm">+20 2 1234 5678</p>
                        <button class="mt-4 bg-pharaoh-blue text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors text-sm" onclick="window.open('tel:19999')">
                            <span data-translate="contact.call_now">اتصل الآن</span>
                        </button>
                    </div>

                    <!-- البريد الإلكتروني -->
                    <div class="contact-card bg-white rounded-lg shadow-lg p-6 text-center">
                        <div class="w-16 h-16 bg-pharaoh-gold rounded-full flex items-center justify-center mx-auto mb-4 animate-float">
                            <span class="text-2xl text-pharaoh-blue">📧</span>
                        </div>
                        <h3 class="text-lg font-bold text-pharaoh-blue mb-2" data-translate="contact.email">
                            البريد الإلكتروني
                        </h3>
                        <p class="text-gray-600 text-sm mb-4" data-translate="contact.email_desc">
                            أرسل لنا رسالة وسنرد عليك خلال 24 ساعة
                        </p>
                        <p class="text-pharaoh-gold font-bold text-sm"><EMAIL></p>
                        <p class="text-gray-500 text-sm"><EMAIL></p>
                        <button class="mt-4 bg-pharaoh-blue text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors text-sm" onclick="window.open('mailto:<EMAIL>')">
                            <span data-translate="contact.send_email">أرسل رسالة</span>
                        </button>
                    </div>

                    <!-- المحادثة المباشرة -->
                    <div class="contact-card bg-white rounded-lg shadow-lg p-6 text-center">
                        <div class="w-16 h-16 bg-pharaoh-gold rounded-full flex items-center justify-center mx-auto mb-4 animate-float">
                            <span class="text-2xl text-pharaoh-blue">💬</span>
                        </div>
                        <h3 class="text-lg font-bold text-pharaoh-blue mb-2" data-translate="contact.live_chat">
                            المحادثة المباشرة
                        </h3>
                        <p class="text-gray-600 text-sm mb-4" data-translate="contact.live_chat_desc">
                            تحدث مع فريق الدعم مباشرة عبر المحادثة
                        </p>
                        <p class="text-pharaoh-gold font-bold text-sm" data-translate="contact.online_now">متاح الآن</p>
                        <p class="text-gray-500 text-sm" data-translate="contact.avg_response">متوسط الرد: دقيقتان</p>
                        <button class="mt-4 bg-pharaoh-blue text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors text-sm" onclick="toggleChat()">
                            <span data-translate="contact.start_chat">ابدأ المحادثة</span>
                        </button>
                    </div>

                    <!-- العنوان -->
                    <div class="contact-card bg-white rounded-lg shadow-lg p-6 text-center">
                        <div class="w-16 h-16 bg-pharaoh-gold rounded-full flex items-center justify-center mx-auto mb-4 animate-float">
                            <span class="text-2xl text-pharaoh-blue">📍</span>
                        </div>
                        <h3 class="text-lg font-bold text-pharaoh-blue mb-2" data-translate="contact.address">
                            العنوان
                        </h3>
                        <p class="text-gray-600 text-sm mb-4" data-translate="contact.address_desc">
                            زورنا في مقرنا الرئيسي في القاهرة
                        </p>
                        <p class="text-pharaoh-gold font-bold text-sm">القاهرة، مصر</p>
                        <p class="text-gray-500 text-sm">شارع التحرير، وسط البلد</p>
                        <button class="mt-4 bg-pharaoh-blue text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors text-sm" onclick="openMap()">
                            <span data-translate="contact.view_map">عرض الخريطة</span>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم نموذج التواصل -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-pharaoh text-pharaoh-blue mb-4" data-translate="contact.send_message">
                            أرسل لنا رسالة
                        </h2>
                        <p class="text-gray-600 max-w-2xl mx-auto" data-translate="contact.send_message_desc">
                            املأ النموذج أدناه وسنتواصل معك في أقرب وقت ممكن
                        </p>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                        <!-- النموذج -->
                        <div class="bg-gray-50 rounded-lg p-8">
                            <form id="contactForm" onsubmit="submitContactForm(event)">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.name">
                                            الاسم الكامل
                                        </label>
                                        <input type="text" name="name" required
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent"
                                               data-translate="form.name" placeholder="أدخل اسمك الكامل">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.email">
                                            البريد الإلكتروني
                                        </label>
                                        <input type="email" name="email" required
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent"
                                               data-translate="form.email" placeholder="أدخل بريدك الإلكتروني">
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.phone">
                                            رقم الهاتف
                                        </label>
                                        <input type="tel" name="phone" required
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent"
                                               data-translate="form.phone" placeholder="أدخل رقم هاتفك">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="contact.subject">
                                            الموضوع
                                        </label>
                                        <select name="subject" required
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                                            <option value="" data-translate="contact.select_subject">اختر الموضوع</option>
                                            <option value="general" data-translate="contact.general_inquiry">استفسار عام</option>
                                            <option value="support" data-translate="contact.technical_support">دعم فني</option>
                                            <option value="complaint" data-translate="contact.complaint">شكوى</option>
                                            <option value="suggestion" data-translate="contact.suggestion">اقتراح</option>
                                            <option value="partnership" data-translate="contact.partnership">شراكة</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-2" data-translate="form.message">
                                        الرسالة
                                    </label>
                                    <textarea name="message" rows="6" required
                                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent"
                                              data-translate="contact.message_placeholder" placeholder="اكتب رسالتك هنا..."></textarea>
                                </div>

                                <div class="mb-6">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="newsletter"
                                               class="rounded border-gray-300 text-pharaoh-gold focus:ring-pharaoh-gold">
                                        <span class="mr-2 text-sm text-gray-600" data-translate="contact.newsletter_subscribe">
                                            أريد الاشتراك في النشرة الإخبارية
                                        </span>
                                    </label>
                                </div>

                                <button type="submit"
                                        class="w-full bg-pharaoh-blue text-white py-4 rounded-lg font-bold text-lg hover:bg-blue-800 transition-colors">
                                    <span data-translate="contact.send_message_btn">إرسال الرسالة</span>
                                </button>
                            </form>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="space-y-8">
                            <!-- ساعات العمل -->
                            <div class="bg-pharaoh-blue text-white rounded-lg p-6">
                                <h3 class="text-xl font-bold mb-4" data-translate="contact.working_hours">
                                    ساعات العمل
                                </h3>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span data-translate="contact.sunday_thursday">الأحد - الخميس</span>
                                        <span>9:00 ص - 6:00 م</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span data-translate="contact.friday">الجمعة</span>
                                        <span>2:00 م - 6:00 م</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span data-translate="contact.saturday">السبت</span>
                                        <span data-translate="contact.closed">مغلق</span>
                                    </div>
                                    <hr class="border-pharaoh-gold/30">
                                    <div class="flex justify-between font-bold text-pharaoh-gold">
                                        <span data-translate="contact.emergency_support">الدعم الطارئ</span>
                                        <span>24/7</span>
                                    </div>
                                </div>
                            </div>

                            <!-- الأسئلة الشائعة -->
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h3 class="text-xl font-bold text-pharaoh-blue mb-4" data-translate="contact.faq">
                                    الأسئلة الشائعة
                                </h3>
                                <div class="space-y-4">
                                    <div>
                                        <h4 class="font-medium text-gray-800 mb-2" data-translate="contact.faq_shipping">
                                            كم يستغرق الشحن؟
                                        </h4>
                                        <p class="text-gray-600 text-sm" data-translate="contact.faq_shipping_answer">
                                            عادة 2-3 أيام عمل داخل القاهرة، و3-5 أيام للمحافظات
                                        </p>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800 mb-2" data-translate="contact.faq_return">
                                            ما هي سياسة الإرجاع؟
                                        </h4>
                                        <p class="text-gray-600 text-sm" data-translate="contact.faq_return_answer">
                                            يمكن إرجاع المنتجات خلال 14 يوم من تاريخ الاستلام
                                        </p>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800 mb-2" data-translate="contact.faq_payment">
                                            ما هي طرق الدفع المتاحة؟
                                        </h4>
                                        <p class="text-gray-600 text-sm" data-translate="contact.faq_payment_answer">
                                            الدفع عند الاستلام، فيزا، ماستركارد، فودافون كاش
                                        </p>
                                    </div>
                                </div>
                                <button class="mt-4 text-pharaoh-blue font-medium hover:text-blue-800 transition-colors" onclick="window.location.href='faq.html'">
                                    <span data-translate="contact.view_all_faq">عرض جميع الأسئلة ←</span>
                                </button>
                            </div>

                            <!-- وسائل التواصل الاجتماعي -->
                            <div class="bg-pharaoh-gold text-pharaoh-blue rounded-lg p-6">
                                <h3 class="text-xl font-bold mb-4" data-translate="contact.follow_us">
                                    تابعنا على
                                </h3>
                                <div class="grid grid-cols-2 gap-4">
                                    <a href="#" class="flex items-center space-x-2 space-x-reverse bg-white/20 rounded-lg p-3 hover:bg-white/30 transition-colors">
                                        <span class="text-xl">📘</span>
                                        <span class="font-medium">Facebook</span>
                                    </a>
                                    <a href="#" class="flex items-center space-x-2 space-x-reverse bg-white/20 rounded-lg p-3 hover:bg-white/30 transition-colors">
                                        <span class="text-xl">📷</span>
                                        <span class="font-medium">Instagram</span>
                                    </a>
                                    <a href="#" class="flex items-center space-x-2 space-x-reverse bg-white/20 rounded-lg p-3 hover:bg-white/30 transition-colors">
                                        <span class="text-xl">🐦</span>
                                        <span class="font-medium">Twitter</span>
                                    </a>
                                    <a href="#" class="flex items-center space-x-2 space-x-reverse bg-white/20 rounded-lg p-3 hover:bg-white/30 transition-colors">
                                        <span class="text-xl">💼</span>
                                        <span class="font-medium">LinkedIn</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        // وظيفة إرسال نموذج التواصل
        function submitContactForm(event) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);

            // إظهار رسالة التحميل
            showNotification(t('msg.saving'), 'info');

            // محاكاة إرسال النموذج
            setTimeout(() => {
                showNotification(t('contact.message_sent', 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً'), 'success');
                form.reset();
            }, 2000);
        }

        // وظيفة فتح الخريطة
        function openMap() {
            const mapUrl = 'https://maps.google.com/maps?q=Cairo,Egypt&hl=ar&z=14&output=embed';
            window.open(mapUrl, '_blank');
        }

        // وظيفة تبديل المحادثة
        function toggleChat() {
            const chatWindow = document.getElementById('chatWindow');
            if (chatWindow) {
                chatWindow.classList.toggle('hidden');
            }
        }
    </script>
</body>
</html>
