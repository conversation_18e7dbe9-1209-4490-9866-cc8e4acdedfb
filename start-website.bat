@echo off
chcp 65001 >nul
title مركز MSB - تشغيل الموقع

echo.
echo ========================================
echo 🏛️  مركز MSB - One Brand, Every Solution
echo ========================================
echo.

REM التحقق من وجود الملفات
if not exist "index.html" (
    echo ❌ خطأ: ملف index.html غير موجود
    echo تأكد من وضع هذا الملف في نفس مجلد الموقع
    pause
    exit /b 1
)

echo ✅ تم العثور على ملفات الموقع
echo.

REM عرض الملفات المتاحة
echo 📋 الملفات المتاحة:
for %%f in (*.html) do echo    - %%f
echo.

REM محاولة تشغيل خادم Python
echo 🚀 محاولة تشغيل خادم محلي...
echo.

REM التحقق من Python 3
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python
    echo 🌐 تشغيل الخادم على http://localhost:8000
    echo.
    echo ⏹️  للإيقاف: اضغط Ctrl+C
    echo ========================================
    echo.
    start http://localhost:8000
    python -m http.server 8000
    goto :end
)

REM التحقق من Python 3 بصيغة أخرى
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python3
    echo 🌐 تشغيل الخادم على http://localhost:8000
    echo.
    echo ⏹️  للإيقاف: اضغط Ctrl+C
    echo ========================================
    echo.
    start http://localhost:8000
    python3 -m http.server 8000
    goto :end
)

REM إذا لم يتم العثور على Python، فتح الملف مباشرة
echo ⚠️  لم يتم العثور على Python
echo 🌐 فتح الملف مباشرة في المتصفح...
echo.

REM فتح الملف في المتصفح الافتراضي
start "" "index.html"

echo ✅ تم فتح الموقع في المتصفح
echo.
echo 📋 يمكنك أيضاً فتح الملفات التالية:
echo    - products.html (المنتجات)
echo    - services.html (الخدمات)
echo    - cart.html (سلة التسوق)
echo    - profile.html (الملف الشخصي)
echo    - orders.html (الطلبات)
echo.

:end
echo ========================================
echo 🎉 شكراً لاستخدام موقع مركز MSB!
echo ========================================
pause
