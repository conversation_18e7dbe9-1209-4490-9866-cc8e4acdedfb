'use client'

import { motion } from 'framer-motion'
import { Users, Package, MapPin, Clock } from 'lucide-react'
import { useLanguage } from '@/hooks/use-language'

export default function StatsSection() {
  const { t, isRTL } = useLanguage()

  const stats = [
    {
      id: 1,
      number: '1000+',
      label: 'منتج متنوع',
      description: 'مجموعة واسعة من المنتجات عالية الجودة',
      icon: Package,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: 2,
      number: '27',
      label: 'محافظة مخدومة',
      description: 'نصل إليك في جميع أنحاء الجمهورية',
      icon: MapPin,
      color: 'from-green-500 to-emerald-500'
    },
    {
      id: 3,
      number: '24/7',
      label: 'ساعات الدعم',
      description: 'فريق دعم متاح على مدار الساعة',
      icon: Clock,
      color: 'from-purple-500 to-pink-500'
    },
    {
      id: 4,
      number: '5000+',
      label: 'عميل راضي',
      description: 'ثقة العملاء هي أولويتنا الأولى',
      icon: Users,
      color: 'from-orange-500 to-red-500'
    }
  ]

  return (
    <section className="py-16 bg-pharaoh-blue relative overflow-hidden">
      {/* الخلفية المزخرفة */}
      <div className="absolute inset-0 hieroglyph-pattern opacity-10"></div>
      
      <div className="container mx-auto px-4 relative">
        {/* العنوان */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-pharaoh text-white mb-4">
            أرقام تتحدث عن نفسها
          </h2>
          <p className="text-lg text-white/80 max-w-2xl mx-auto">
            نفخر بالثقة التي وضعها عملاؤنا فينا والإنجازات التي حققناها معاً
          </p>
        </motion.div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              whileHover={{ y: -5 }}
              className="group"
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 text-center text-white hover:bg-white/20 transition-all duration-300 border border-white/20">
                {/* الأيقونة */}
                <div className="relative mb-6">
                  <div className={`w-20 h-20 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <stat.icon className="w-10 h-10 text-white" />
                  </div>
                </div>

                {/* الرقم */}
                <motion.div
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  transition={{ delay: index * 0.1 + 0.3, duration: 0.5, type: "spring" }}
                  className="text-4xl md:text-5xl font-bold text-pharaoh-gold mb-2"
                >
                  {stat.number}
                </motion.div>

                {/* التسمية */}
                <h3 className="text-xl font-bold mb-3">
                  {stat.label}
                </h3>

                {/* الوصف */}
                <p className="text-white/80 text-sm leading-relaxed">
                  {stat.description}
                </p>

                {/* خط الزينة */}
                <div className={`w-16 h-1 bg-gradient-to-r ${stat.color} rounded-full mx-auto mt-4 group-hover:w-24 transition-all duration-300`}></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* قسم إضافي للإنجازات */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.6 }}
          className="mt-16 text-center"
        >
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <h3 className="text-2xl font-bold text-white mb-6">
              إنجازاتنا في أرقام
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {[
                { number: '99%', label: 'معدل الرضا' },
                { number: '2 ساعة', label: 'متوسط الاستجابة' },
                { number: '48 ساعة', label: 'متوسط التوصيل' },
                { number: '365 يوم', label: 'ضمان الخدمة' }
              ].map((achievement, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 + 0.8, duration: 0.4 }}
                  className="text-center"
                >
                  <div className="text-2xl font-bold text-pharaoh-gold mb-1">
                    {achievement.number}
                  </div>
                  <div className="text-white/80 text-sm">
                    {achievement.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* تأثيرات بصرية */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-pharaoh-gold/20 rounded-full blur-xl"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-white/10 rounded-full blur-2xl"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-pharaoh-gold/30 rounded-full blur-lg"></div>
      </div>
    </section>
  )
}
