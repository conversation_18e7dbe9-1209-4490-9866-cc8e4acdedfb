{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder/New%20folder/e-commerce-site/src/app/page.tsx"], "sourcesContent": ["\"use client\";\nimport {useTranslations} from 'next-intl';\n \nexport default function Home() {\n  const t = useTranslations('HomePage');\n  return (\n    <main className=\"flex min-h-screen flex-col items-center justify-center p-24\">\n      <h1 className=\"text-4xl font-bold\">{t('title')}</h1>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AADA;;AAGe,SAAS;;IACtB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,qBACE,6LAAC;QAAK,WAAU;kBACd,cAAA,6LAAC;YAAG,WAAU;sBAAsB,EAAE;;;;;;;;;;;AAG5C;GAPwB;;QACZ,yMAAA,CAAA,kBAAe;;;KADH", "debugId": null}}]}