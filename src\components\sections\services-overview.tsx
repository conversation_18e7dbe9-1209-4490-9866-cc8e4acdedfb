'use client'

import { motion } from 'framer-motion'
import { Wrench, Truck, Shield, Headphones, Clock, Award } from 'lucide-react'
import { useLanguage } from '@/hooks/use-language'

export default function ServicesOverview() {
  const { t, isRTL } = useLanguage()

  const services = [
    {
      id: 1,
      title: 'خدمات الصيانة',
      description: 'صيانة احترافية لجميع أنواع الأجهزة مع ضمان الجودة',
      icon: Wrench,
      features: ['فنيين معتمدين', 'قطع غيار أصلية', 'ضمان 6 أشهر'],
      color: 'from-blue-500 to-cyan-500',
      price: 'من 150 ج.م',
      duration: '2-4 ساعات'
    },
    {
      id: 2,
      title: 'التوصيل السريع',
      description: 'خدمة توصيل سريعة وآمنة لجميع أنحاء الجمهورية',
      icon: Truck,
      features: ['توصيل خلال 24 ساعة', 'تتبع الطلب', 'تأمين الشحنة'],
      color: 'from-green-500 to-emerald-500',
      price: 'من 25 ج.م',
      duration: '1-3 أيام'
    },
    {
      id: 3,
      title: 'الضمان الشامل',
      description: 'ضمان شامل على جميع المنتجات والخدمات',
      icon: Shield,
      features: ['ضمان سنة كاملة', 'استبدال فوري', 'صيانة مجانية'],
      color: 'from-purple-500 to-pink-500',
      price: 'مجاني',
      duration: 'حتى سنة'
    },
    {
      id: 4,
      title: 'دعم العملاء',
      description: 'فريق دعم متخصص متاح 24/7 لخدمتك',
      icon: Headphones,
      features: ['دعم مباشر', 'استجابة سريعة', 'حلول فورية'],
      color: 'from-orange-500 to-red-500',
      price: 'مجاني',
      duration: '24/7'
    },
    {
      id: 5,
      title: 'الخدمة السريعة',
      description: 'خدمات سريعة ومضمونة في أقل وقت ممكن',
      icon: Clock,
      features: ['خدمة فورية', 'مواعيد مرنة', 'أولوية الطوارئ'],
      color: 'from-teal-500 to-blue-500',
      price: 'حسب الخدمة',
      duration: 'فوري'
    },
    {
      id: 6,
      title: 'الجودة المضمونة',
      description: 'التزام بأعلى معايير الجودة في جميع خدماتنا',
      icon: Award,
      features: ['معايير دولية', 'فحص دقيق', 'رضا العملاء'],
      color: 'from-yellow-500 to-orange-500',
      price: 'مضمون',
      duration: 'دائم'
    }
  ]

  return (
    <section className="py-16 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4">
        {/* العنوان */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-pharaoh text-gray-900 dark:text-white mb-4">
            خدماتنا المتميزة
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            نقدم مجموعة شاملة من الخدمات المتخصصة لضمان رضاكم التام
          </p>
        </motion.div>

        {/* شبكة الخدمات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              whileHover={{ y: -5 }}
              className="group relative"
            >
              <div className="relative bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
                {/* الخلفية المتدرجة */}
                <div className={`absolute top-0 left-0 w-full h-2 bg-gradient-to-r ${service.color}`}></div>
                
                {/* الأيقونة */}
                <div className="relative mb-6">
                  <div className={`w-16 h-16 bg-gradient-to-r ${service.color} rounded-xl flex items-center justify-center mb-4`}>
                    <service.icon className="w-8 h-8 text-white" />
                  </div>
                  
                  {/* معلومات سريعة */}
                  <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                    <span>{service.price}</span>
                    <span>{service.duration}</span>
                  </div>
                </div>

                {/* المحتوى */}
                <div className="space-y-4">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                    {service.title}
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    {service.description}
                  </p>

                  {/* المميزات */}
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                        <div className={`w-2 h-2 bg-gradient-to-r ${service.color} rounded-full flex-shrink-0`}></div>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>

                  {/* زر العمل */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className={`w-full mt-6 bg-gradient-to-r ${service.color} text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300`}
                  >
                    اطلب الخدمة
                  </motion.button>
                </div>

                {/* تأثير الهوفر */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* قسم الحجز السريع */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="mt-16 bg-pharaoh-blue rounded-2xl p-8 text-center text-white relative overflow-hidden"
        >
          {/* الخلفية المزخرفة */}
          <div className="absolute inset-0 hieroglyph-pattern opacity-10"></div>
          
          <div className="relative">
            <h3 className="text-2xl font-pharaoh mb-4">هل تحتاج خدمة عاجلة؟</h3>
            <p className="text-lg mb-6 text-white/90 max-w-2xl mx-auto">
              احجز موعدك الآن واحصل على خدمة سريعة ومتخصصة من فريقنا المحترف
            </p>
            
            {/* نموذج الحجز السريع */}
            <div className="max-w-md mx-auto">
              <div className="flex flex-col sm:flex-row gap-4">
                <input
                  type="tel"
                  placeholder="رقم الهاتف"
                  className="flex-1 px-4 py-3 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/70 focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent"
                />
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-pharaoh-gold text-pharaoh-blue px-6 py-3 rounded-lg font-bold hover:bg-pharaoh-gold/90 transition-all duration-300"
                >
                  احجز الآن
                </motion.button>
              </div>
              <p className="text-sm text-white/70 mt-3">
                سنتصل بك خلال 15 دقيقة لتأكيد الموعد
              </p>
            </div>
          </div>
        </motion.div>

        {/* إحصائيات الخدمات */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.6 }}
          className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8"
        >
          {[
            { number: '5000+', label: 'عميل راضي' },
            { number: '24/7', label: 'دعم متواصل' },
            { number: '99%', label: 'معدل الرضا' },
            { number: '2 ساعة', label: 'متوسط الاستجابة' }
          ].map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl font-bold text-pharaoh-blue dark:text-pharaoh-gold mb-2">
                {stat.number}
              </div>
              <div className="text-gray-600 dark:text-gray-400">
                {stat.label}
              </div>
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
