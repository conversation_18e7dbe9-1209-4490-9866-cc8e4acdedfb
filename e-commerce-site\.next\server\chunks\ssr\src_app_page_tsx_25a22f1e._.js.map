{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder/New%20folder/e-commerce-site/src/app/page.tsx"], "sourcesContent": ["\"use client\";\nimport {useTranslations} from 'next-intl';\n \nexport default function Home() {\n  const t = useTranslations('HomePage');\n  return (\n    <main className=\"flex min-h-screen flex-col items-center justify-center p-24\">\n      <h1 className=\"text-4xl font-bold\">{t('title')}</h1>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAGe,SAAS;IACtB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,qBACE,8OAAC;QAAK,WAAU;kBACd,cAAA,8OAAC;YAAG,WAAU;sBAAsB,EAAE;;;;;;;;;;;AAG5C", "debugId": null}}]}