<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate="nav.products">المنتجات - مركز MSB</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

    <!-- ملفات الترجمة والوظائف المشتركة -->
    <script src="js/translations.js"></script>
    <script src="js/common.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'pharaoh-gold': '#D4AF37',
                        'pharaoh-blue': '#003366',
                        'pharaoh-sand': '#F4E4BC',
                        'pharaoh-copper': '#B87333',
                    },
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                        'pharaoh': ['Amiri', 'serif'],
                    }
                }
            }
        }
    </script>
    <style>
        .hieroglyph-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E");
        }

        /* الوضع الليلي */
        .dark {
            background-color: #1a1a1a !important;
            color: #ffffff !important;
        }

        .dark header {
            background-color: #2d2d2d !important;
            border-bottom: 1px solid #444 !important;
        }

        .dark .bg-white {
            background-color: #2d2d2d !important;
            color: #ffffff !important;
        }

        .dark .text-gray-900 {
            color: #ffffff !important;
        }

        .dark .text-gray-700 {
            color: #cccccc !important;
        }

        .dark .text-gray-600 {
            color: #aaaaaa !important;
        }

        .dark .bg-gray-50 {
            background-color: #1a1a1a !important;
        }

        .dark .bg-gray-100 {
            background-color: #333333 !important;
        }

        .dark .border-gray-300 {
            border-color: #555555 !important;
        }

        .dark .shadow-lg {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3) !important;
        }

        .dark footer {
            background-color: #111111 !important;
        }
    </style>
</head>
<body class="font-arabic">
    <!-- شريط العروض -->
    <div class="bg-pharaoh-gold text-pharaoh-blue py-2 text-center text-sm font-medium animate-pulse">
        <div class="container mx-auto px-4">
            🎉 عروض خاصة - خصم يصل إلى 50% على جميع المنتجات! 🎉
        </div>
    </div>

    <!-- الهيدر -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <!-- الهيدر الرئيسي -->
            <div class="flex items-center justify-between h-16">
                <!-- الشعار -->
                <div class="flex items-center space-x-2 space-x-reverse">
                    <div class="w-10 h-10 bg-pharaoh-gold rounded-lg flex items-center justify-center">
                        <span class="text-pharaoh-blue font-bold text-xl">M</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-pharaoh text-pharaoh-blue" data-translate="site.title">مركز MSB</h1>
                        <p class="text-xs text-gray-600" data-translate="site.tagline">One Brand, Every Solution</p>
                    </div>
                </div>

                <!-- شريط البحث -->
                <div class="hidden lg:flex flex-1 max-w-xl mx-8">
                    <div class="relative w-full">
                        <input type="text" data-translate="search.placeholder" placeholder="ابحث عن المنتجات والخدمات..."
                               class="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                        <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            🔍
                        </div>
                    </div>
                </div>

                <!-- أدوات الهيدر -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button id="themeToggle" class="p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="theme.toggle" title="تبديل الثيم">🌙</button>

                    <!-- قائمة اللغات -->
                    <div class="relative">
                        <button id="languageToggle" class="flex items-center p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="language.change" title="تغيير اللغة">
                            <span id="currentFlag">🇪🇬</span>
                            <span class="ml-1 text-sm font-medium" id="currentLang">العربية</span>
                        </button>
                        <div id="languageMenu" class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border hidden z-50">
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors language-option" data-lang="ar" data-flag="🇪🇬" data-name="العربية">
                                <span class="mr-2">🇪🇬</span>
                                <span data-translate="language.arabic">العربية</span>
                                <span class="mr-auto text-green-500" id="check-ar">✓</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors language-option" data-lang="en" data-flag="🇺🇸" data-name="English">
                                <span class="mr-2">🇺🇸</span>
                                <span data-translate="language.english">English</span>
                                <span class="mr-auto text-green-500 hidden" id="check-en">✓</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors language-option" data-lang="fr" data-flag="🇫🇷" data-name="Français">
                                <span class="mr-2">🇫🇷</span>
                                <span data-translate="language.french">Français</span>
                                <span class="mr-auto text-green-500 hidden" id="check-fr">✓</span>
                            </a>
                        </div>
                    </div>

                    <button class="relative p-2 rounded-lg hover:bg-gray-100 transition-colors wishlist-counter" data-translate-title="nav.wishlist" title="المفضلة" onclick="window.location.href='wishlist.html'">
                        ❤️
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-pharaoh-gold text-pharaoh-blue text-xs rounded-full flex items-center justify-center font-bold">3</span>
                    </button>
                    <button class="relative p-2 rounded-lg hover:bg-gray-100 transition-colors cart-counter" data-translate-title="nav.cart" title="سلة التسوق" onclick="window.location.href='cart.html'">
                        🛒
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-pharaoh-gold text-pharaoh-blue text-xs rounded-full flex items-center justify-center font-bold">2</span>
                    </button>

                    <!-- قائمة المستخدم -->
                    <div class="relative">
                        <button id="userToggle" class="p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="nav.account" title="حسابي">👤</button>
                        <div id="userMenu" class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border hidden z-50">
                            <div class="px-4 py-2 border-b border-gray-200">
                                <p class="text-sm font-medium text-gray-900">أحمد محمد</p>
                                <p class="text-xs text-gray-500"><EMAIL></p>
                            </div>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="profile">
                                <span class="mr-2">👤</span>
                                <span data-translate="nav.account">حسابي</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="orders">
                                <span class="mr-2">📦</span>
                                <span data-translate="nav.orders">طلباتي</span>
                                <span class="mr-auto bg-pharaoh-gold text-pharaoh-blue text-xs px-2 py-1 rounded-full">3</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="wishlist">
                                <span class="mr-2">❤️</span>
                                <span data-translate="nav.wishlist">المفضلة</span>
                                <span class="mr-auto bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">5</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="settings">
                                <span class="mr-2">⚙️</span>
                                <span data-translate="nav.settings">الإعدادات</span>
                            </a>
                            <hr class="my-1">
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-red-50 transition-colors text-red-600 user-option" data-action="logout">
                                <span class="mr-2">🚪</span>
                                <span data-translate="nav.logout">تسجيل الخروج</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التنقل الرئيسي -->
            <nav class="hidden lg:flex items-center justify-center py-4 border-t border-gray-200">
                <div class="flex items-center space-x-8 space-x-reverse">
                    <a href="index.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.home">الرئيسية</a>
                    <a href="products.html" class="text-pharaoh-blue font-bold transition-colors" data-translate="nav.products">المنتجات</a>
                    <a href="services.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.services">الخدمات</a>
                    <a href="about.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.about">من نحن</a>
                    <a href="contact.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.contact">اتصل بنا</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- شريط التنقل -->
    <div class="bg-pharaoh-blue text-white py-3">
        <div class="container mx-auto px-4">
            <div class="flex items-center space-x-2 space-x-reverse text-sm">
                <a href="index.html" class="hover:text-pharaoh-gold" data-translate="nav.home">الرئيسية</a>
                <span>←</span>
                <span class="text-pharaoh-gold" data-translate="nav.products">المنتجات</span>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <main class="container mx-auto px-4 py-8">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- الشريط الجانبي للفلاتر -->
            <aside class="lg:w-1/4">
                <div class="bg-white rounded-lg shadow-lg p-6 sticky top-24">
                    <h3 class="text-xl font-bold mb-6 text-pharaoh-blue" data-translate="filter.title">تصفية المنتجات</h3>

                    <!-- البحث -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-2" data-translate="search.placeholder">البحث</label>
                        <input type="text" data-translate="search.placeholder" placeholder="ابحث عن منتج..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold">
                    </div>

                    <!-- الفئات -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-2" data-translate="filter.category">الفئات</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded text-pharaoh-gold">
                                <span class="mr-2">الإلكترونيات (45)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded text-pharaoh-gold">
                                <span class="mr-2">المنزل والحديقة (32)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded text-pharaoh-gold">
                                <span class="mr-2">الأزياء (28)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded text-pharaoh-gold">
                                <span class="mr-2">الرياضة (15)</span>
                            </label>
                        </div>
                    </div>

                    <!-- نطاق السعر -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-2" data-translate="filter.price">نطاق السعر</label>
                        <div class="flex space-x-2 space-x-reverse">
                            <input type="number" placeholder="من" class="w-1/2 px-3 py-2 border border-gray-300 rounded-lg">
                            <input type="number" placeholder="إلى" class="w-1/2 px-3 py-2 border border-gray-300 rounded-lg">
                        </div>
                    </div>

                    <!-- التقييم -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-2" data-translate="filter.rating">التقييم</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="rating" class="text-pharaoh-gold">
                                <span class="mr-2">⭐⭐⭐⭐⭐ (5 نجوم)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="rating" class="text-pharaoh-gold">
                                <span class="mr-2">⭐⭐⭐⭐ (4 نجوم فأكثر)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="rating" class="text-pharaoh-gold">
                                <span class="mr-2">⭐⭐⭐ (3 نجوم فأكثر)</span>
                            </label>
                        </div>
                    </div>

                    <button class="w-full bg-pharaoh-gold text-pharaoh-blue py-2 rounded-lg font-bold hover:bg-yellow-300 transition-colors" data-translate="filter.apply">
                        تطبيق الفلاتر
                    </button>
                </div>
            </aside>

            <!-- منطقة المنتجات -->
            <div class="lg:w-3/4">
                <!-- شريط الترتيب -->
                <div class="bg-white rounded-lg shadow-lg p-4 mb-6">
                    <div class="flex flex-col md:flex-row justify-between items-center">
                        <div class="mb-4 md:mb-0">
                            <span class="text-gray-600">عرض 1-12 من 120 منتج</span>
                        </div>
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <label class="text-sm">ترتيب حسب:</label>
                            <select class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold">
                                <option>الأحدث</option>
                                <option>الأكثر مبيعاً</option>
                                <option>السعر: من الأقل للأعلى</option>
                                <option>السعر: من الأعلى للأقل</option>
                                <option>التقييم</option>
                            </select>
                            <div class="flex border border-gray-300 rounded-lg">
                                <button class="p-2 bg-pharaoh-gold text-pharaoh-blue">⊞</button>
                                <button class="p-2 hover:bg-gray-100">☰</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- شبكة المنتجات -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- منتج 1 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="relative">
                            <div class="h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                <span class="text-4xl">📱</span>
                            </div>
                            <div class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs">-20%</div>
                            <div class="absolute top-2 left-2 bg-pharaoh-gold text-pharaoh-blue px-2 py-1 rounded text-xs">جديد</div>
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="bg-white p-2 rounded-full hover:bg-pharaoh-gold hover:text-pharaoh-blue">👁️</button>
                                    <button class="bg-white p-2 rounded-full hover:bg-red-500 hover:text-white">❤️</button>
                                </div>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center mb-2">
                                <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                                <span class="text-sm text-gray-600 mr-2">(124)</span>
                            </div>
                            <h3 class="font-bold text-lg mb-2">هاتف ذكي متطور</h3>
                            <p class="text-gray-600 text-sm mb-3">هاتف ذكي بمواصفات عالية وكاميرا متطورة</p>
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <span class="text-xl font-bold text-pharaoh-blue">15,999 ج.م</span>
                                    <span class="text-sm text-gray-500 line-through mr-2">19,999 ج.م</span>
                                </div>
                            </div>
                            <button class="w-full bg-pharaoh-blue text-white py-2 rounded-lg hover:bg-blue-800 transition-colors">
                                أضف للسلة
                            </button>
                        </div>
                    </div>

                    <!-- منتج 2 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="relative">
                            <div class="h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                <span class="text-4xl">💻</span>
                            </div>
                            <div class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs">-15%</div>
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="bg-white p-2 rounded-full hover:bg-pharaoh-gold hover:text-pharaoh-blue">👁️</button>
                                    <button class="bg-white p-2 rounded-full hover:bg-red-500 hover:text-white">❤️</button>
                                </div>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center mb-2">
                                <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                                <span class="text-sm text-gray-600 mr-2">(89)</span>
                            </div>
                            <h3 class="font-bold text-lg mb-2">لابتوب للألعاب</h3>
                            <p class="text-gray-600 text-sm mb-3">لابتوب قوي مخصص للألعاب والتصميم</p>
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <span class="text-xl font-bold text-pharaoh-blue">45,999 ج.م</span>
                                    <span class="text-sm text-gray-500 line-through mr-2">52,999 ج.م</span>
                                </div>
                            </div>
                            <button class="w-full bg-pharaoh-blue text-white py-2 rounded-lg hover:bg-blue-800 transition-colors">
                                أضف للسلة
                            </button>
                        </div>
                    </div>

                    <!-- منتج 3 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="relative">
                            <div class="h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                <span class="text-4xl">⌚</span>
                            </div>
                            <div class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs">-25%</div>
                            <div class="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-xs">متوفر</div>
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="bg-white p-2 rounded-full hover:bg-pharaoh-gold hover:text-pharaoh-blue">👁️</button>
                                    <button class="bg-white p-2 rounded-full hover:bg-red-500 hover:text-white">❤️</button>
                                </div>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center mb-2">
                                <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                                <span class="text-sm text-gray-600 mr-2">(156)</span>
                            </div>
                            <h3 class="font-bold text-lg mb-2">ساعة ذكية رياضية</h3>
                            <p class="text-gray-600 text-sm mb-3">ساعة ذكية مقاومة للماء مع GPS</p>
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <span class="text-xl font-bold text-pharaoh-blue">3,999 ج.م</span>
                                    <span class="text-sm text-gray-500 line-through mr-2">4,999 ج.م</span>
                                </div>
                            </div>
                            <button class="w-full bg-pharaoh-blue text-white py-2 rounded-lg hover:bg-blue-800 transition-colors">
                                أضف للسلة
                            </button>
                        </div>
                    </div>

                    <!-- منتج 4 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="relative">
                            <div class="h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                <span class="text-4xl">🎧</span>
                            </div>
                            <div class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs">-30%</div>
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="bg-white p-2 rounded-full hover:bg-pharaoh-gold hover:text-pharaoh-blue">👁️</button>
                                    <button class="bg-white p-2 rounded-full hover:bg-red-500 hover:text-white">❤️</button>
                                </div>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center mb-2">
                                <span class="text-yellow-400">⭐⭐⭐⭐</span>
                                <span class="text-sm text-gray-600 mr-2">(78)</span>
                            </div>
                            <h3 class="font-bold text-lg mb-2">سماعات لاسلكية</h3>
                            <p class="text-gray-600 text-sm mb-3">سماعات عالية الجودة مع إلغاء الضوضاء</p>
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <span class="text-xl font-bold text-pharaoh-blue">1,999 ج.م</span>
                                    <span class="text-sm text-gray-500 line-through mr-2">2,899 ج.م</span>
                                </div>
                            </div>
                            <button class="w-full bg-pharaoh-blue text-white py-2 rounded-lg hover:bg-blue-800 transition-colors">
                                أضف للسلة
                            </button>
                        </div>
                    </div>

                    <!-- منتج 5 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="relative">
                            <div class="h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                <span class="text-4xl">📷</span>
                            </div>
                            <div class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs">-18%</div>
                            <div class="absolute top-2 left-2 bg-pharaoh-gold text-pharaoh-blue px-2 py-1 rounded text-xs">مميز</div>
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="bg-white p-2 rounded-full hover:bg-pharaoh-gold hover:text-pharaoh-blue">👁️</button>
                                    <button class="bg-white p-2 rounded-full hover:bg-red-500 hover:text-white">❤️</button>
                                </div>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center mb-2">
                                <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                                <span class="text-sm text-gray-600 mr-2">(203)</span>
                            </div>
                            <h3 class="font-bold text-lg mb-2">كاميرا رقمية احترافية</h3>
                            <p class="text-gray-600 text-sm mb-3">كاميرا عالية الدقة للمصورين المحترفين</p>
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <span class="text-xl font-bold text-pharaoh-blue">25,999 ج.م</span>
                                    <span class="text-sm text-gray-500 line-through mr-2">31,999 ج.م</span>
                                </div>
                            </div>
                            <button class="w-full bg-pharaoh-blue text-white py-2 rounded-lg hover:bg-blue-800 transition-colors">
                                أضف للسلة
                            </button>
                        </div>
                    </div>

                    <!-- منتج 6 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="relative">
                            <div class="h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                <span class="text-4xl">🖥️</span>
                            </div>
                            <div class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs">-12%</div>
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="bg-white p-2 rounded-full hover:bg-pharaoh-gold hover:text-pharaoh-blue">👁️</button>
                                    <button class="bg-white p-2 rounded-full hover:bg-red-500 hover:text-white">❤️</button>
                                </div>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center mb-2">
                                <span class="text-yellow-400">⭐⭐⭐⭐</span>
                                <span class="text-sm text-gray-600 mr-2">(95)</span>
                            </div>
                            <h3 class="font-bold text-lg mb-2">شاشة كمبيوتر 4K</h3>
                            <p class="text-gray-600 text-sm mb-3">شاشة عالية الدقة مثالية للعمل والألعاب</p>
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <span class="text-xl font-bold text-pharaoh-blue">8,999 ج.م</span>
                                    <span class="text-sm text-gray-500 line-through mr-2">10,299 ج.م</span>
                                </div>
                            </div>
                            <button class="w-full bg-pharaoh-blue text-white py-2 rounded-lg hover:bg-blue-800 transition-colors">
                                أضف للسلة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- التنقل بين الصفحات -->
                <div class="mt-8 flex justify-center">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button class="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-100">السابق</button>
                        <button class="px-3 py-2 bg-pharaoh-gold text-pharaoh-blue rounded-lg font-bold">1</button>
                        <button class="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-100">2</button>
                        <button class="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-100">3</button>
                        <span class="px-3 py-2">...</span>
                        <button class="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-100">10</button>
                        <button class="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-100">التالي</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- الفوتر -->
    <footer class="bg-gray-900 text-white mt-16">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center">
                <div class="flex items-center justify-center space-x-2 space-x-reverse mb-4">
                    <div class="w-12 h-12 bg-pharaoh-gold rounded-lg flex items-center justify-center">
                        <span class="text-pharaoh-blue font-bold text-xl">M</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-pharaoh text-white">مركز MSB</h3>
                        <p class="text-sm text-pharaoh-gold">One Brand, Every Solution</p>
                    </div>
                </div>
                <p class="text-gray-400 mb-4">© 2024 مركز MSB. جميع الحقوق محفوظة.</p>
                <div class="flex justify-center space-x-4 space-x-reverse">
                    <a href="index.html" class="text-gray-400 hover:text-pharaoh-gold">الرئيسية</a>
                    <a href="products.html" class="text-pharaoh-gold">المنتجات</a>
                    <a href="services.html" class="text-gray-400 hover:text-pharaoh-gold">الخدمات</a>
                    <a href="about.html" class="text-gray-400 hover:text-pharaoh-gold">من نحن</a>
                    <a href="contact.html" class="text-gray-400 hover:text-pharaoh-gold">اتصل بنا</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // وظيفة تبديل الثيم
        function toggleTheme() {
            const body = document.body;
            const themeToggle = document.getElementById('themeToggle');

            if (body.classList.contains('dark')) {
                body.classList.remove('dark');
                themeToggle.textContent = '🌙';
                localStorage.setItem('theme', 'light');
            } else {
                body.classList.add('dark');
                themeToggle.textContent = '☀️';
                localStorage.setItem('theme', 'dark');
            }
        }

        // تحميل الثيم المحفوظ
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme');
            const themeToggle = document.getElementById('themeToggle');

            if (savedTheme === 'dark') {
                document.body.classList.add('dark');
                themeToggle.textContent = '☀️';
            }

            // ربط زر تبديل الثيم
            themeToggle.addEventListener('click', toggleTheme);

            // قائمة اللغات
            const languageToggle = document.getElementById('languageToggle');
            const languageMenu = document.getElementById('languageMenu');

            languageToggle.addEventListener('click', function(e) {
                e.stopPropagation();
                languageMenu.classList.toggle('hidden');
                userMenu.classList.add('hidden'); // إغلاق قائمة المستخدم
            });

            // قائمة المستخدم
            const userToggle = document.getElementById('userToggle');
            const userMenu = document.getElementById('userMenu');

            userToggle.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
                languageMenu.classList.add('hidden'); // إغلاق قائمة اللغات
            });

            // إغلاق القوائم عند النقر خارجها
            document.addEventListener('click', function() {
                languageMenu.classList.add('hidden');
                userMenu.classList.add('hidden');
            });
        });
    </script>
</body>
</html>
