export default function HomePage() {
  return (
    <div className="min-h-screen" dir="rtl">
      {/* شريط العروض */}
      <div className="bg-yellow-400 text-blue-900 py-2 text-center text-sm font-medium">
        <div className="container mx-auto px-4">
          🎉 عروض خاصة - خصم يصل إلى 50% على جميع المنتجات! 🎉
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <main className="relative">
        {/* قسم البطل */}
        <section className="py-20 bg-gradient-to-r from-blue-900 to-yellow-600 text-white">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              مرحباً بك في مركز MSB
            </h1>
            <p className="text-xl mb-8">
              One Brand, Every Solution - كل ما تحتاجه تحت سقف واحد
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-yellow-400 text-blue-900 px-8 py-4 rounded-lg font-bold text-lg hover:bg-yellow-300 transition-colors">
                تسوق الآن
              </button>
              <button className="bg-transparent border-2 border-white text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-white hover:text-blue-900 transition-colors">
                اطلب خدمة
              </button>
            </div>
          </div>
        </section>

        {/* قسم المنتجات */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-900">
              المنتجات المميزة
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[1, 2, 3].map((item) => (
                <div key={item} className="bg-white rounded-lg shadow-lg p-6">
                  <div className="h-48 bg-gray-200 rounded-lg mb-4 flex items-center justify-center">
                    <span className="text-gray-500">صورة المنتج {item}</span>
                  </div>
                  <h3 className="text-xl font-bold mb-2">منتج رقم {item}</h3>
                  <p className="text-gray-600 mb-4">وصف المنتج هنا...</p>
                  <div className="flex justify-between items-center">
                    <span className="text-2xl font-bold text-blue-900">999 ج.م</span>
                    <button className="bg-blue-900 text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors">
                      أضف للسلة
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* قسم الخدمات */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-900">
              خدماتنا المتميزة
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { title: 'خدمات الصيانة', icon: '🔧' },
                { title: 'التوصيل السريع', icon: '🚚' },
                { title: 'الضمان الشامل', icon: '🛡️' },
                { title: 'دعم العملاء', icon: '📞' }
              ].map((service, index) => (
                <div key={index} className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-bold mb-2">{service.title}</h3>
                  <p className="text-gray-600">وصف الخدمة هنا...</p>
                </div>
              ))}
            </div>
          </div>
        </section>
      </main>

      {/* الفوتر */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">مركز MSB</h3>
              <p className="text-gray-400">
                مركز MSB المصري للتجارة الإلكترونية والخدمات المتكاملة
              </p>
            </div>
            <div>
              <h4 className="font-bold mb-4">روابط سريعة</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">من نحن</a></li>
                <li><a href="#" className="hover:text-white">المنتجات</a></li>
                <li><a href="#" className="hover:text-white">الخدمات</a></li>
                <li><a href="#" className="hover:text-white">اتصل بنا</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-bold mb-4">خدمة العملاء</h4>
              <ul className="space-y-2 text-gray-400">
                <li>📞 +20 100 123 4567</li>
                <li>✉️ <EMAIL></li>
                <li>📍 القاهرة، مصر</li>
              </ul>
            </div>
            <div>
              <h4 className="font-bold mb-4">تابعنا</h4>
              <div className="flex space-x-4 rtl:space-x-reverse">
                <a href="#" className="text-gray-400 hover:text-white">📘</a>
                <a href="#" className="text-gray-400 hover:text-white">📷</a>
                <a href="#" className="text-gray-400 hover:text-white">🐦</a>
                <a href="#" className="text-gray-400 hover:text-white">📺</a>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>© 2024 مركز MSB. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
