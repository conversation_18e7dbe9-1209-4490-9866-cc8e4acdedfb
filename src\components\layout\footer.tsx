'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { 
  Facebook, 
  Instagram, 
  Twitter, 
  Youtube, 
  Linkedin,
  Phone,
  Mail,
  MapPin,
  Clock,
  CreditCard,
  Truck,
  Shield,
  Award
} from 'lucide-react'
import { useLanguage } from '@/hooks/use-language'

export default function Footer() {
  const { t, isRTL } = useLanguage()

  const socialLinks = [
    { icon: Facebook, href: '#', label: 'Facebook', color: 'hover:text-blue-600' },
    { icon: Instagram, href: '#', label: 'Instagram', color: 'hover:text-pink-600' },
    { icon: Twitter, href: '#', label: 'Twitter', color: 'hover:text-blue-400' },
    { icon: Youtube, href: '#', label: 'YouTube', color: 'hover:text-red-600' },
    { icon: Linkedin, href: '#', label: 'LinkedIn', color: 'hover:text-blue-700' },
  ]

  const quickLinks = [
    { href: '/about', label: 'من نحن' },
    { href: '/privacy', label: 'سياسة الخصوصية' },
    { href: '/terms', label: 'الشروط والأحكام' },
    { href: '/shipping', label: 'سياسة الشحن' },
    { href: '/returns', label: 'سياسة الإرجاع' },
    { href: '/faq', label: 'الأسئلة الشائعة' },
  ]

  const productCategories = [
    { href: '/products/electronics', label: 'الإلكترونيات' },
    { href: '/products/home', label: 'المنزل والحديقة' },
    { href: '/products/fashion', label: 'الأزياء' },
    { href: '/products/sports', label: 'الرياضة' },
    { href: '/products/books', label: 'الكتب' },
    { href: '/products/automotive', label: 'السيارات' },
  ]

  const services = [
    { href: '/services/repair', label: 'خدمات الصيانة' },
    { href: '/services/installation', label: 'التركيب' },
    { href: '/services/consultation', label: 'الاستشارات' },
    { href: '/services/warranty', label: 'الضمان' },
    { href: '/services/support', label: 'الدعم الفني' },
    { href: '/services/training', label: 'التدريب' },
  ]

  const features = [
    { icon: Truck, text: 'شحن مجاني للطلبات أكثر من 500 جنيه' },
    { icon: Shield, text: 'ضمان الجودة والأصالة' },
    { icon: CreditCard, text: 'طرق دفع آمنة ومتنوعة' },
    { icon: Award, text: 'خدمة عملاء متميزة 24/7' },
  ]

  return (
    <footer className="bg-gray-900 text-white">
      {/* قسم الميزات */}
      <div className="border-b border-gray-800">
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center space-x-3 rtl:space-x-reverse"
              >
                <div className="flex-shrink-0 w-12 h-12 bg-pharaoh-gold rounded-lg flex items-center justify-center">
                  <feature.icon className="w-6 h-6 text-pharaoh-blue" />
                </div>
                <p className="text-sm text-gray-300">{feature.text}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* معلومات الشركة */}
          <div className="space-y-6">
            <div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse mb-4">
                <div className="w-12 h-12 bg-pharaoh-gold rounded-lg flex items-center justify-center">
                  <span className="text-pharaoh-blue font-bold text-xl">M</span>
                </div>
                <div>
                  <h3 className="text-xl font-pharaoh text-white">مركز MSB</h3>
                  <p className="text-sm text-pharaoh-gold">One Brand, Every Solution</p>
                </div>
              </div>
              <p className="text-gray-400 text-sm leading-relaxed">
                مركز MSB هو وجهتك الأولى للتجارة الإلكترونية في مصر. نقدم أفضل المنتجات والخدمات 
                بجودة عالية وأسعار تنافسية مع خدمة عملاء متميزة.
              </p>
            </div>

            {/* معلومات الاتصال */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Phone className="w-5 h-5 text-pharaoh-gold flex-shrink-0" />
                <span className="text-sm text-gray-300">+20 ************</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Mail className="w-5 h-5 text-pharaoh-gold flex-shrink-0" />
                <span className="text-sm text-gray-300"><EMAIL></span>
              </div>
              <div className="flex items-start space-x-3 rtl:space-x-reverse">
                <MapPin className="w-5 h-5 text-pharaoh-gold flex-shrink-0 mt-0.5" />
                <span className="text-sm text-gray-300">
                  شارع التحرير، وسط البلد، القاهرة، مصر
                </span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Clock className="w-5 h-5 text-pharaoh-gold flex-shrink-0" />
                <span className="text-sm text-gray-300">السبت - الخميس: 9:00 ص - 10:00 م</span>
              </div>
            </div>
          </div>

          {/* روابط سريعة */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-white">روابط سريعة</h4>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <Link 
                    href={link.href}
                    className="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* فئات المنتجات */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-white">فئات المنتجات</h4>
            <ul className="space-y-3">
              {productCategories.map((category, index) => (
                <li key={index}>
                  <Link 
                    href={category.href}
                    className="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm"
                  >
                    {category.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* الخدمات */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-white">خدماتنا</h4>
            <ul className="space-y-3">
              {services.map((service, index) => (
                <li key={index}>
                  <Link 
                    href={service.href}
                    className="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm"
                  >
                    {service.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* النشرة الإخبارية */}
        <div className="mt-12 pt-8 border-t border-gray-800">
          <div className="max-w-md mx-auto text-center">
            <h4 className="text-lg font-semibold mb-4 text-white">اشترك في النشرة الإخبارية</h4>
            <p className="text-gray-400 text-sm mb-6">
              احصل على آخر العروض والمنتجات الجديدة مباشرة في بريدك الإلكتروني
            </p>
            <div className="flex space-x-2 rtl:space-x-reverse">
              <input
                type="email"
                placeholder="أدخل بريدك الإلكتروني"
                className="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent text-white placeholder-gray-400"
              />
              <button className="px-6 py-2 bg-pharaoh-gold text-pharaoh-blue rounded-lg hover:bg-pharaoh-gold/90 transition-colors font-medium">
                اشتراك
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* الجزء السفلي */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            {/* حقوق النشر */}
            <div className="text-center md:text-right rtl:md:text-left">
              <p className="text-gray-400 text-sm">
                © 2024 مركز MSB. جميع الحقوق محفوظة.
              </p>
            </div>

            {/* وسائل التواصل الاجتماعي */}
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  className={`w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 transition-colors ${social.color}`}
                  aria-label={social.label}
                >
                  <social.icon className="w-5 h-5" />
                </motion.a>
              ))}
            </div>

            {/* طرق الدفع */}
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <span className="text-gray-400 text-sm">طرق الدفع:</span>
              <div className="flex space-x-2 rtl:space-x-reverse">
                <div className="w-8 h-6 bg-blue-600 rounded text-white text-xs flex items-center justify-center font-bold">
                  VISA
                </div>
                <div className="w-8 h-6 bg-red-600 rounded text-white text-xs flex items-center justify-center font-bold">
                  MC
                </div>
                <div className="w-8 h-6 bg-green-600 rounded text-white text-xs flex items-center justify-center font-bold">
                  VF
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
