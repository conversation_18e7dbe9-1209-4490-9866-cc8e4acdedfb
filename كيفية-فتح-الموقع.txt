🏛️ مركز MSB - دليل فتح الموقع
=====================================

📋 الملفات الموجودة في المشروع:
- index.html (الصفحة الرئيسية)
- products.html (المنتجات)
- services.html (الخدمات)
- about.html (من نحن)
- contact.html (اتصل بنا)
- cart.html (سلة التسوق)
- wishlist.html (المفضلة)
- profile.html (الملف الشخصي)
- orders.html (الطلبات)
- start.html (صفحة البداية)

📁 مجلد js/:
- translations.js (نظام الترجمة)
- common.js (الوظائف المشتركة)

🔧 ملفات التشغيل:
- start-website.bat (للويندوز)
- run-server.py (خادم Python)

=====================================
🚀 طرق فتح الموقع:
=====================================

الطريقة الأولى - الفتح المباشر:
1. افتح مستكشف الملفات (File Explorer)
2. انتقل إلى مجلد المشروع
3. ابحث عن ملف "index.html"
4. انقر عليه نقرة مزدوجة
5. سيفتح في المتصفح الافتراضي

الطريقة الثانية - استخدام ملف التشغيل:
1. انقر نقرة مزدوجة على "start-website.bat"
2. سيفتح الموقع تلقائياً

الطريقة الثالثة - السحب والإفلات:
1. افتح متصفح الإنترنت (Chrome, Firefox, Edge)
2. اسحب ملف "index.html" إلى نافذة المتصفح
3. أفلت الملف وسيفتح الموقع

الطريقة الرابعة - خادم محلي:
1. تأكد من تثبيت Python على جهازك
2. انقر نقرة مزدوجة على "run-server.py"
3. أو افتح Command Prompt في مجلد المشروع
4. اكتب: python -m http.server 8000
5. افتح المتصفح واذهب إلى: http://localhost:8000

الطريقة الخامسة - VS Code:
1. افتح VS Code
2. افتح مجلد المشروع (File > Open Folder)
3. انقر بالزر الأيمن على "index.html"
4. اختر "Open with Live Server" (إذا كان مثبت)

=====================================
🎯 ما ستجده في الموقع:
=====================================

✅ الترجمة الفورية:
- انقر على زر اللغة في الهيدر (🇪🇬 العربية)
- اختر من: العربية، English، Français
- الترجمة تحدث فوراً دون إعادة تحميل

✅ تبديل الثيم:
- انقر على زر القمر (🌙) في الهيدر
- التبديل بين الوضع النهاري والليلي
- يحفظ تفضيلك تلقائياً

✅ المساعد الذكي:
- أيقونة عائمة (🤖) في الزاوية اليمنى السفلية
- انقر عليها لفتح نافذة المحادثة
- اكتب رسالة واحصل على رد تلقائي

✅ التسوق التفاعلي:
- أزرار "أضف للسلة" تعمل مع عداد حي
- أزرار "أضف للمفضلة" تعمل مع عداد
- جميع الروابط والأزرار فعالة

✅ التنقل السلس:
- جميع صفحات الموقع مرتبطة
- التنقل يعمل بسلاسة
- تصميم متجاوب لجميع الأجهزة

=====================================
🔧 حل المشاكل الشائعة:
=====================================

❌ المشكلة: "الملف غير موجود"
✅ الحل: تأكد من أنك في المجلد الصحيح الذي يحتوي على index.html

❌ المشكلة: "الصفحة لا تحمل بشكل صحيح"
✅ الحل: استخدم خادم محلي بدلاً من الفتح المباشر

❌ المشكلة: "الترجمة لا تعمل"
✅ الحل: تأكد من تحميل ملفات JavaScript (translations.js, common.js)

❌ المشكلة: "الأزرار لا تعمل"
✅ الحل: تأكد من السماح بتشغيل JavaScript في المتصفح

=====================================
📞 الدعم:
=====================================

إذا واجهت أي مشكلة:
1. تأكد من وجود جميع الملفات في نفس المجلد
2. جرب طريقة فتح مختلفة
3. تأكد من تحديث المتصفح
4. جرب متصفح آخر

🏛️ مركز MSB - One Brand, Every Solution
شكراً لاستخدام موقعنا! 🎉
