"use client";
import Link from 'next/link';
import { useTranslations } from 'next-intl';

export default function Header() {
  const t = useTranslations('Header');
  return (
    <header className="bg-gray-900 text-white p-4">
      <div className="container mx-auto flex justify-between items-center">
        <Link href="/" className="text-2xl font-bold text-yellow-400">
          MSB Center
        </Link>
        <nav>
          <ul className="flex space-x-4">
            <li><Link href="/products" className="hover:text-yellow-400">{t('products')}</Link></li>
            <li><Link href="/services" className="hover:text-yellow-400">{t('services')}</Link></li>
            <li><Link href="/about" className="hover:text-yellow-400">{t('about')}</Link></li>
            <li><Link href="/contact" className="hover:text-yellow-400">{t('contact')}</Link></li>
          </ul>
        </nav>
        <div>
          {/* Auth buttons will go here */}
        </div>
      </div>
    </header>
  );
}