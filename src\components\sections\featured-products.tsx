'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Star, Heart, ShoppingCart, Eye, ArrowLeft, ArrowRight } from 'lucide-react'
import { useLanguage } from '@/hooks/use-language'

export default function FeaturedProducts() {
  const { t, isRTL } = useLanguage()
  const [activeCategory, setActiveCategory] = useState('all')

  const categories = [
    { id: 'all', name: 'الكل', count: 12 },
    { id: 'electronics', name: 'إلكترونيات', count: 5 },
    { id: 'home', name: 'المنزل', count: 3 },
    { id: 'fashion', name: 'أزياء', count: 2 },
    { id: 'sports', name: 'رياضة', count: 2 }
  ]

  const products = [
    {
      id: 1,
      name: 'هاتف ذكي متطور',
      price: 15999,
      originalPrice: 18999,
      rating: 4.8,
      reviews: 124,
      image: '/product-1.jpg',
      category: 'electronics',
      badge: 'الأكثر مبيعاً',
      discount: 16,
      inStock: true,
      features: ['شاشة OLED', 'كاميرا 108MP', 'بطارية 5000mAh']
    },
    {
      id: 2,
      name: 'لابتوب للألعاب',
      price: 45999,
      originalPrice: 52999,
      rating: 4.9,
      reviews: 89,
      image: '/product-2.jpg',
      category: 'electronics',
      badge: 'جديد',
      discount: 13,
      inStock: true,
      features: ['RTX 4060', 'Intel i7', '16GB RAM']
    },
    {
      id: 3,
      name: 'طقم أواني المطبخ',
      price: 2499,
      originalPrice: 3199,
      rating: 4.6,
      reviews: 67,
      image: '/product-3.jpg',
      category: 'home',
      badge: 'عرض خاص',
      discount: 22,
      inStock: true,
      features: ['ستانلس ستيل', '12 قطعة', 'مقاوم للخدش']
    },
    {
      id: 4,
      name: 'ساعة ذكية رياضية',
      price: 3999,
      originalPrice: 4999,
      rating: 4.7,
      reviews: 156,
      image: '/product-4.jpg',
      category: 'sports',
      badge: 'مقاوم للماء',
      discount: 20,
      inStock: false,
      features: ['GPS', 'مراقب نبضات', 'بطارية 7 أيام']
    },
    {
      id: 5,
      name: 'حقيبة يد نسائية',
      price: 1299,
      originalPrice: 1599,
      rating: 4.5,
      reviews: 43,
      image: '/product-5.jpg',
      category: 'fashion',
      badge: 'تصميم حصري',
      discount: 19,
      inStock: true,
      features: ['جلد طبيعي', 'متعددة الجيوب', 'مقاومة للماء']
    },
    {
      id: 6,
      name: 'مكنسة كهربائية ذكية',
      price: 8999,
      originalPrice: 10999,
      rating: 4.8,
      reviews: 92,
      image: '/product-6.jpg',
      category: 'home',
      badge: 'توفير الطاقة',
      discount: 18,
      inStock: true,
      features: ['تحكم ذكي', 'فلتر HEPA', 'صوت منخفض']
    }
  ]

  const filteredProducts = activeCategory === 'all' 
    ? products 
    : products.filter(product => product.category === activeCategory)

  return (
    <section className="py-16 bg-white dark:bg-gray-800">
      <div className="container mx-auto px-4">
        {/* العنوان */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-pharaoh text-gray-900 dark:text-white mb-4">
            المنتجات المميزة
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            اكتشف مجموعة مختارة من أفضل المنتجات عالية الجودة
          </p>
        </motion.div>

        {/* فلاتر الفئات */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                activeCategory === category.id
                  ? 'bg-pharaoh-gold text-pharaoh-blue shadow-lg'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              {category.name} ({category.count})
            </button>
          ))}
        </motion.div>

        {/* شبكة المنتجات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProducts.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              className="group relative bg-white dark:bg-gray-900 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
            >
              {/* الصورة */}
              <div className="relative h-64 overflow-hidden">
                {/* شارة الخصم */}
                {product.discount > 0 && (
                  <div className="absolute top-4 right-4 rtl:right-auto rtl:left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10">
                    -{product.discount}%
                  </div>
                )}

                {/* شارة المنتج */}
                <div className="absolute top-4 left-4 rtl:left-auto rtl:right-4 bg-pharaoh-gold text-pharaoh-blue px-3 py-1 rounded-full text-sm font-bold z-10">
                  {product.badge}
                </div>

                {/* الصورة الوهمية */}
                <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center">
                  <div className="text-center text-gray-500 dark:text-gray-400">
                    <div className="w-16 h-16 bg-pharaoh-gold/20 rounded-full flex items-center justify-center mx-auto mb-2">
                      <ShoppingCart className="w-8 h-8 text-pharaoh-gold" />
                    </div>
                    <p className="text-sm">صورة المنتج</p>
                  </div>
                </div>

                {/* أزرار التفاعل */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-gray-700 hover:bg-pharaoh-gold hover:text-pharaoh-blue transition-all duration-300"
                    >
                      <Eye className="w-5 h-5" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-gray-700 hover:bg-red-500 hover:text-white transition-all duration-300"
                    >
                      <Heart className="w-5 h-5" />
                    </motion.button>
                  </div>
                </div>

                {/* حالة المخزون */}
                {!product.inStock && (
                  <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                    <span className="bg-red-500 text-white px-4 py-2 rounded-lg font-bold">
                      نفد من المخزون
                    </span>
                  </div>
                )}
              </div>

              {/* تفاصيل المنتج */}
              <div className="p-6">
                {/* التقييم */}
                <div className="flex items-center space-x-2 rtl:space-x-reverse mb-3">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-4 h-4 ${
                          i < Math.floor(product.rating)
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {product.rating} ({product.reviews} تقييم)
                  </span>
                </div>

                {/* اسم المنتج */}
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2 line-clamp-2">
                  {product.name}
                </h3>

                {/* المميزات */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {product.features.slice(0, 2).map((feature, index) => (
                    <span
                      key={index}
                      className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded"
                    >
                      {feature}
                    </span>
                  ))}
                </div>

                {/* السعر */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <span className="text-2xl font-bold text-pharaoh-blue dark:text-pharaoh-gold">
                      {product.price.toLocaleString()} ج.م
                    </span>
                    {product.originalPrice > product.price && (
                      <span className="text-sm text-gray-500 line-through">
                        {product.originalPrice.toLocaleString()} ج.م
                      </span>
                    )}
                  </div>
                </div>

                {/* زر الإضافة للسلة */}
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  disabled={!product.inStock}
                  className={`w-full py-3 rounded-lg font-medium transition-all duration-300 flex items-center justify-center space-x-2 rtl:space-x-reverse ${
                    product.inStock
                      ? 'bg-pharaoh-blue text-white hover:bg-pharaoh-blue/90'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  <ShoppingCart className="w-5 h-5" />
                  <span>{product.inStock ? 'أضف للسلة' : 'غير متوفر'}</span>
                </motion.button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* زر عرض المزيد */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="text-center mt-12"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-pharaoh-gold text-pharaoh-blue px-8 py-4 rounded-lg font-bold hover:bg-pharaoh-gold/90 transition-all duration-300"
          >
            <span>عرض جميع المنتجات</span>
            {isRTL ? <ArrowLeft className="w-5 h-5" /> : <ArrowRight className="w-5 h-5" />}
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}
