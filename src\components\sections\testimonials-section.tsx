'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Star, Quote, ChevronLeft, ChevronRight, User } from 'lucide-react'
import { useLanguage } from '@/hooks/use-language'

export default function TestimonialsSection() {
  const { t, isRTL } = useLanguage()
  const [currentTestimonial, setCurrentTestimonial] = useState(0)

  const testimonials = [
    {
      id: 1,
      name: 'أحمد محمد',
      location: 'القاهرة',
      rating: 5,
      comment: 'خدمة ممتازة وسرعة في التوصيل. المنتجات أصلية وبجودة عالية. أنصح الجميع بالتعامل مع مركز MSB.',
      service: 'شراء لابتوب',
      date: '2024-01-15',
      avatar: '/avatar-1.jpg'
    },
    {
      id: 2,
      name: 'فاطمة علي',
      location: 'الإسكندرية',
      rating: 5,
      comment: 'فريق الصيانة محترف جداً وحل مشكلة جهازي في وقت قياسي. الأسعار معقولة والضمان ممتاز.',
      service: 'صيانة هاتف',
      date: '2024-01-10',
      avatar: '/avatar-2.jpg'
    },
    {
      id: 3,
      name: 'محمود حسن',
      location: 'الجيزة',
      rating: 4,
      comment: 'تجربة رائعة في التسوق الإلكتروني. الموقع سهل الاستخدام وخدمة العملاء متجاوبة جداً.',
      service: 'شراء أجهزة منزلية',
      date: '2024-01-08',
      avatar: '/avatar-3.jpg'
    },
    {
      id: 4,
      name: 'نورا أحمد',
      location: 'المنصورة',
      rating: 5,
      comment: 'أفضل مركز للتجارة الإلكترونية في مصر. التوصيل سريع والمنتجات كما هو موضح بالضبط.',
      service: 'شراء إكسسوارات',
      date: '2024-01-05',
      avatar: '/avatar-4.jpg'
    },
    {
      id: 5,
      name: 'خالد عبدالله',
      location: 'أسوان',
      rating: 5,
      comment: 'حتى في المحافظات البعيدة، الخدمة ممتازة والتوصيل في الوقت المحدد. شكراً لفريق MSB.',
      service: 'شراء قطع غيار',
      date: '2024-01-03',
      avatar: '/avatar-5.jpg'
    }
  ]

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  return (
    <section className="py-16 bg-white dark:bg-gray-800">
      <div className="container mx-auto px-4">
        {/* العنوان */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-pharaoh text-gray-900 dark:text-white mb-4">
            آراء عملائنا
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            نفخر بثقة عملائنا وآرائهم الإيجابية حول خدماتنا ومنتجاتنا
          </p>
        </motion.div>

        {/* التقييم الإجمالي */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-pharaoh-gold/10 px-6 py-3 rounded-full">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
              ))}
            </div>
            <span className="text-lg font-bold text-pharaoh-blue dark:text-pharaoh-gold">4.9</span>
            <span className="text-gray-600 dark:text-gray-400">من 5 (1,234 تقييم)</span>
          </div>
        </motion.div>

        {/* الشهادات الرئيسية */}
        <div className="relative max-w-4xl mx-auto mb-12">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentTestimonial}
              initial={{ opacity: 0, x: isRTL ? -50 : 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: isRTL ? 50 : -50 }}
              transition={{ duration: 0.5 }}
              className="bg-gray-50 dark:bg-gray-900 rounded-2xl p-8 md:p-12 relative"
            >
              {/* أيقونة الاقتباس */}
              <div className="absolute top-6 right-6 rtl:right-auto rtl:left-6 w-12 h-12 bg-pharaoh-gold/20 rounded-full flex items-center justify-center">
                <Quote className="w-6 h-6 text-pharaoh-gold" />
              </div>

              {/* التقييم */}
              <div className="flex items-center space-x-1 rtl:space-x-reverse mb-6">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-5 h-5 ${
                      i < testimonials[currentTestimonial].rating
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>

              {/* النص */}
              <blockquote className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 leading-relaxed mb-8 font-medium">
                "{testimonials[currentTestimonial].comment}"
              </blockquote>

              {/* معلومات العميل */}
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <div className="w-16 h-16 bg-pharaoh-gold/20 rounded-full flex items-center justify-center">
                  <User className="w-8 h-8 text-pharaoh-gold" />
                </div>
                <div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-white">
                    {testimonials[currentTestimonial].name}
                  </h4>
                  <p className="text-gray-600 dark:text-gray-400">
                    {testimonials[currentTestimonial].location} • {testimonials[currentTestimonial].service}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-500">
                    {new Date(testimonials[currentTestimonial].date).toLocaleDateString('ar-EG')}
                  </p>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>

          {/* أزرار التنقل */}
          <button
            onClick={prevTestimonial}
            className="absolute left-4 rtl:left-auto rtl:right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white dark:bg-gray-800 rounded-full shadow-lg flex items-center justify-center text-gray-600 dark:text-gray-400 hover:bg-pharaoh-gold hover:text-pharaoh-blue transition-all duration-300"
          >
            {isRTL ? <ChevronRight className="w-6 h-6" /> : <ChevronLeft className="w-6 h-6" />}
          </button>

          <button
            onClick={nextTestimonial}
            className="absolute right-4 rtl:right-auto rtl:left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white dark:bg-gray-800 rounded-full shadow-lg flex items-center justify-center text-gray-600 dark:text-gray-400 hover:bg-pharaoh-gold hover:text-pharaoh-blue transition-all duration-300"
          >
            {isRTL ? <ChevronLeft className="w-6 h-6" /> : <ChevronRight className="w-6 h-6" />}
          </button>
        </div>

        {/* مؤشرات الشهادات */}
        <div className="flex justify-center space-x-2 rtl:space-x-reverse mb-12">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentTestimonial
                  ? 'bg-pharaoh-gold scale-125'
                  : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
              }`}
            />
          ))}
        </div>

        {/* شبكة الشهادات المصغرة */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {testimonials.slice(0, 3).map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              className="bg-gray-50 dark:bg-gray-900 rounded-xl p-6 hover:shadow-lg transition-all duration-300"
            >
              {/* التقييم */}
              <div className="flex items-center space-x-1 rtl:space-x-reverse mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < testimonial.rating
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>

              {/* النص المختصر */}
              <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed mb-4 line-clamp-3">
                "{testimonial.comment}"
              </p>

              {/* معلومات العميل */}
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-10 h-10 bg-pharaoh-gold/20 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-pharaoh-gold" />
                </div>
                <div>
                  <h5 className="font-medium text-gray-900 dark:text-white text-sm">
                    {testimonial.name}
                  </h5>
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                    {testimonial.location}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* دعوة للعمل */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="text-center mt-12"
        >
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
            شاركنا تجربتك
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            رأيك يهمنا ويساعدنا على تحسين خدماتنا
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-pharaoh-gold text-pharaoh-blue px-8 py-3 rounded-lg font-bold hover:bg-pharaoh-gold/90 transition-all duration-300"
          >
            اكتب تقييمك
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}
