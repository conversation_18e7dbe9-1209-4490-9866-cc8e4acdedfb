{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_2c4b2d10._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_1f9ae7ce.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(ar|en))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(ar|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xOz67q10l7JPrsdMsfQnL3VENZvByFu6JuBkr3ZKlFo=", "__NEXT_PREVIEW_MODE_ID": "df9f52ecf11a19f989bbf0440ad8a398", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "85651d318b47c428dfe2ece9ef09077936ff326282b09ddc48d85779d298e752", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3bca95f7c90a6668e302ff7914c3045be2f20f62aefe8759a5b2d88ba83eb3da"}}}, "sortedMiddleware": ["/"], "functions": {}}