/**
 * مكون تبديل اللغة المتقدم لمركز MSB
 * Advanced Language Switcher Component for MSB Center
 */

class LanguageSwitcher {
    constructor(containerId = 'languageSwitcher') {
        this.containerId = containerId;
        this.container = null;
        this.isOpen = false;
        this.currentLanguage = 'ar';
        this.languages = [
            { 
                code: 'ar', 
                name: 'العربية', 
                nativeName: 'العربية', 
                flag: '🇪🇬',
                direction: 'rtl'
            },
            { 
                code: 'en', 
                name: 'English', 
                nativeName: 'English', 
                flag: '🇺🇸',
                direction: 'ltr'
            },
            { 
                code: 'fr', 
                name: 'Français', 
                nativeName: 'Français', 
                flag: '🇫🇷',
                direction: 'ltr'
            }
        ];
        
        this.init();
    }

    /**
     * تهيئة مكون تبديل اللغة
     */
    init() {
        this.container = document.getElementById(this.containerId);
        if (!this.container) {
            console.error(`Language switcher container with ID "${this.containerId}" not found`);
            return;
        }

        this.currentLanguage = this.getCurrentLanguage();
        this.render();
        this.attachEventListeners();
        this.setupKeyboardNavigation();
    }

    /**
     * الحصول على اللغة الحالية
     */
    getCurrentLanguage() {
        return localStorage.getItem('msb-language') || 
               document.documentElement.lang || 
               'ar';
    }

    /**
     * رسم مكون تبديل اللغة
     */
    render() {
        const currentLang = this.languages.find(lang => lang.code === this.currentLanguage);
        
        this.container.innerHTML = `
            <div class="language-switcher relative">
                <!-- زر تبديل اللغة -->
                <button 
                    id="languageToggle"
                    class="language-toggle flex items-center space-x-2 space-x-reverse px-3 py-2 rounded-lg bg-white/10 hover:bg-white/20 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-pharaoh-gold"
                    aria-haspopup="true"
                    aria-expanded="false"
                    aria-label="تغيير اللغة"
                >
                    <span class="text-lg">${currentLang.flag}</span>
                    <span class="text-sm font-medium text-white">${currentLang.nativeName}</span>
                    <svg class="language-arrow w-4 h-4 text-white transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <!-- قائمة اللغات -->
                <div 
                    id="languageDropdown"
                    class="language-dropdown absolute top-full mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 opacity-0 invisible transform scale-95 transition-all duration-200 z-50"
                    role="menu"
                    aria-labelledby="languageToggle"
                >
                    <div class="py-2">
                        ${this.languages.map(lang => this.renderLanguageOption(lang)).join('')}
                    </div>
                    
                    <!-- معلومات إضافية -->
                    <div class="border-t border-gray-200 px-4 py-3 bg-gray-50 rounded-b-lg">
                        <p class="text-xs text-gray-600 text-center">
                            <span class="inline-block w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                            <span data-translate="language.auto_detect">كشف تلقائي للغة</span>
                        </p>
                    </div>
                </div>

                <!-- مؤشر التحميل -->
                <div id="languageLoader" class="language-loader hidden absolute inset-0 flex items-center justify-center bg-white/90 rounded-lg">
                    <div class="animate-spin rounded-full h-5 w-5 border-2 border-pharaoh-gold border-t-transparent"></div>
                </div>
            </div>
        `;
    }

    /**
     * رسم خيار لغة واحد
     */
    renderLanguageOption(language) {
        const isActive = language.code === this.currentLanguage;
        
        return `
            <button
                class="language-option w-full flex items-center space-x-3 space-x-reverse px-4 py-3 text-right hover:bg-pharaoh-gold/10 transition-colors duration-150 ${isActive ? 'bg-pharaoh-gold/20 text-pharaoh-blue' : 'text-gray-700'}"
                data-language="${language.code}"
                role="menuitem"
                ${isActive ? 'aria-current="true"' : ''}
            >
                <span class="text-xl">${language.flag}</span>
                <div class="flex-1">
                    <div class="font-medium">${language.nativeName}</div>
                    <div class="text-xs text-gray-500">${language.name}</div>
                </div>
                ${isActive ? '<span class="text-pharaoh-gold">✓</span>' : ''}
            </button>
        `;
    }

    /**
     * ربط مستمعي الأحداث
     */
    attachEventListeners() {
        const toggle = document.getElementById('languageToggle');
        const dropdown = document.getElementById('languageDropdown');

        // فتح/إغلاق القائمة
        toggle.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleDropdown();
        });

        // اختيار لغة
        dropdown.addEventListener('click', (e) => {
            const option = e.target.closest('.language-option');
            if (option) {
                const languageCode = option.dataset.language;
                this.changeLanguage(languageCode);
            }
        });

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                this.closeDropdown();
            }
        });

        // إغلاق القائمة عند الضغط على Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeDropdown();
                toggle.focus();
            }
        });

        // مراقبة تغيير اللغة من مصادر أخرى
        document.addEventListener('languageChanged', (e) => {
            this.currentLanguage = e.detail.language;
            this.render();
            this.attachEventListeners();
        });
    }

    /**
     * إعداد التنقل بلوحة المفاتيح
     */
    setupKeyboardNavigation() {
        const dropdown = document.getElementById('languageDropdown');
        
        dropdown.addEventListener('keydown', (e) => {
            const options = dropdown.querySelectorAll('.language-option');
            const currentIndex = Array.from(options).findIndex(option => 
                option === document.activeElement
            );

            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    const nextIndex = (currentIndex + 1) % options.length;
                    options[nextIndex].focus();
                    break;
                    
                case 'ArrowUp':
                    e.preventDefault();
                    const prevIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
                    options[prevIndex].focus();
                    break;
                    
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    if (document.activeElement.classList.contains('language-option')) {
                        document.activeElement.click();
                    }
                    break;
            }
        });
    }

    /**
     * تبديل حالة القائمة المنسدلة
     */
    toggleDropdown() {
        if (this.isOpen) {
            this.closeDropdown();
        } else {
            this.openDropdown();
        }
    }

    /**
     * فتح القائمة المنسدلة
     */
    openDropdown() {
        const toggle = document.getElementById('languageToggle');
        const dropdown = document.getElementById('languageDropdown');
        const arrow = toggle.querySelector('.language-arrow');

        this.isOpen = true;
        toggle.setAttribute('aria-expanded', 'true');
        dropdown.classList.remove('opacity-0', 'invisible', 'scale-95');
        dropdown.classList.add('opacity-100', 'visible', 'scale-100');
        arrow.style.transform = 'rotate(180deg)';

        // تركيز على أول خيار
        setTimeout(() => {
            const firstOption = dropdown.querySelector('.language-option');
            if (firstOption) firstOption.focus();
        }, 100);
    }

    /**
     * إغلاق القائمة المنسدلة
     */
    closeDropdown() {
        const toggle = document.getElementById('languageToggle');
        const dropdown = document.getElementById('languageDropdown');
        const arrow = toggle.querySelector('.language-arrow');

        this.isOpen = false;
        toggle.setAttribute('aria-expanded', 'false');
        dropdown.classList.add('opacity-0', 'invisible', 'scale-95');
        dropdown.classList.remove('opacity-100', 'visible', 'scale-100');
        arrow.style.transform = 'rotate(0deg)';
    }

    /**
     * تغيير اللغة
     */
    async changeLanguage(languageCode) {
        if (languageCode === this.currentLanguage) {
            this.closeDropdown();
            return;
        }

        // إظهار مؤشر التحميل
        this.showLoader();

        try {
            // تغيير اللغة باستخدام مدير الترجمة
            if (window.i18nManager) {
                await window.i18nManager.changeLanguage(languageCode);
            } else if (window.changeLanguage) {
                await window.changeLanguage(languageCode);
            }

            // تحديث اللغة الحالية
            this.currentLanguage = languageCode;

            // إعادة رسم المكون
            this.render();
            this.attachEventListeners();

            // إظهار رسالة نجاح
            this.showSuccessMessage(languageCode);

        } catch (error) {
            console.error('خطأ في تغيير اللغة:', error);
            this.showErrorMessage();
        } finally {
            this.hideLoader();
            this.closeDropdown();
        }
    }

    /**
     * إظهار مؤشر التحميل
     */
    showLoader() {
        const loader = document.getElementById('languageLoader');
        if (loader) {
            loader.classList.remove('hidden');
        }
    }

    /**
     * إخفاء مؤشر التحميل
     */
    hideLoader() {
        const loader = document.getElementById('languageLoader');
        if (loader) {
            loader.classList.add('hidden');
        }
    }

    /**
     * إظهار رسالة نجاح
     */
    showSuccessMessage(languageCode) {
        const language = this.languages.find(lang => lang.code === languageCode);
        if (language && window.showNotification) {
            window.showNotification(
                `تم تغيير اللغة إلى ${language.nativeName}`,
                'success'
            );
        }
    }

    /**
     * إظهار رسالة خطأ
     */
    showErrorMessage() {
        if (window.showNotification) {
            window.showNotification(
                'حدث خطأ في تغيير اللغة',
                'error'
            );
        }
    }

    /**
     * تحديث اللغة الحالية من الخارج
     */
    updateCurrentLanguage(languageCode) {
        this.currentLanguage = languageCode;
        this.render();
        this.attachEventListeners();
    }

    /**
     * الحصول على معلومات اللغة الحالية
     */
    getCurrentLanguageInfo() {
        return this.languages.find(lang => lang.code === this.currentLanguage);
    }

    /**
     * تدمير المكون
     */
    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}

// تهيئة مكون تبديل اللغة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // البحث عن جميع حاويات تبديل اللغة
    const containers = document.querySelectorAll('[data-language-switcher]');
    
    containers.forEach(container => {
        new LanguageSwitcher(container.id);
    });

    // إنشاء مكون افتراضي إذا وُجد العنصر
    if (document.getElementById('languageSwitcher')) {
        window.languageSwitcher = new LanguageSwitcher();
    }
});

// تصدير الفئة للاستخدام
window.LanguageSwitcher = LanguageSwitcher;
