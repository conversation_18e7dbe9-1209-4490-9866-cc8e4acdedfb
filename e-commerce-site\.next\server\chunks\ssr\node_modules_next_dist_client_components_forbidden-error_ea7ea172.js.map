{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder/New%20folder/e-commerce-site/node_modules/next/src/client/components/forbidden-error.tsx"], "sourcesContent": ["import { HTTPAccessErrorFallback } from './http-access-fallback/error-fallback'\n\nexport default function Forbidden() {\n  return (\n    <HTTPAccessErrorFallback\n      status={403}\n      message=\"This page could not be accessed.\"\n    />\n  )\n}\n"], "names": ["Forbidden", "HTTPAccessErrorFallback", "status", "message"], "mappings": ";;;;+BAEA,WAAA;;;eAAwBA;;;;+BAFgB;AAEzB,SAASA;IACtB,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACC,eAAAA,uBAAuB,EAAA;QACtBC,QAAQ;QACRC,SAAQ;;AAGd", "ignoreList": [0], "debugId": null}}]}