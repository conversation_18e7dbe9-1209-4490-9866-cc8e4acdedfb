# مركز MSB - موقع التجارة الإلكترونية

## نظرة عامة
مركز MSB هو موقع تجارة إلكترونية متكامل باللغة العربية مع تصميم فرعوني أصيل. يوفر الموقع تجربة تسوق شاملة مع دعم متعدد اللغات ووظائف تفاعلية متقدمة.

## المميزات الرئيسية

### 🌐 **متعدد اللغات**
- دعم 3 لغات: العربية، الإنجليزية، الفرنسية
- نظام ترجمة تلقائي شامل
- تبديل اللغة الفوري

### 🎨 **التصميم**
- تصميم فرعوني أصيل بألوان الذهب والأزرق
- تصميم متجاوب لجميع الأجهزة
- تأثيرات حركية سلسة
- دعم الثيم المظلم والفاتح

### 🛒 **وظائف التجارة الإلكترونية**
- عرض المنتجات مع فلترة وترتيب متقدم
- سلة التسوق التفاعلية
- قائمة المفضلة
- نظام إدارة الطلبات
- تتبع الطلبات في الوقت الفعلي

### 👤 **إدارة المستخدمين**
- ملف شخصي شامل
- إدارة العناوين
- إعدادات الأمان والخصوصية
- المصادقة الثنائية
- إدارة الجلسات النشطة

### 🔧 **الخدمات**
- نظام حجز الخدمات
- دعم فني متاح 24/7
- محادثة مباشرة
- نظام التقييمات

## هيكل المشروع

```
msb-center/
├── index.html              # الصفحة الرئيسية
├── products.html           # صفحة المنتجات
├── services.html           # صفحة الخدمات
├── about.html              # صفحة من نحن
├── contact.html            # صفحة اتصل بنا
├── cart.html               # سلة التسوق
├── wishlist.html           # المفضلة
├── profile.html            # الملف الشخصي
├── orders.html             # الطلبات
├── js/
│   ├── translations.js     # نظام الترجمة
│   └── common.js           # الوظائف المشتركة
└── README.md               # هذا الملف
```

## الصفحات المتاحة

### 🏠 **الصفحة الرئيسية** (`index.html`)
- عرض المنتجات المميزة
- العروض والخصومات
- الفئات الرئيسية
- شهادات العملاء

### 🛍️ **صفحة المنتجات** (`products.html`)
- عرض جميع المنتجات
- فلترة متقدمة (الفئة، السعر، التقييم)
- ترتيب متعدد المعايير
- بحث ذكي

### 🔧 **صفحة الخدمات** (`services.html`)
- عرض الخدمات المتاحة
- نظام حجز الخدمات
- معلومات الدعم الفني
- نموذج طلب الخدمة

### ℹ️ **صفحة من نحن** (`about.html`)
- قصة الشركة
- الرؤية والمهمة
- إحصائيات الشركة
- تاريخ التأسيس

### 📞 **صفحة اتصل بنا** (`contact.html`)
- معلومات الاتصال
- نموذج التواصل
- الأسئلة الشائعة
- ساعات العمل

### 🛒 **سلة التسوق** (`cart.html`)
- عرض المنتجات المضافة
- تحديث الكميات
- حساب المجاميع
- كود الخصم
- إتمام الشراء

### ❤️ **المفضلة** (`wishlist.html`)
- عرض المنتجات المفضلة
- إضافة للسلة
- ترتيب وفلترة
- إدارة القائمة

### 👤 **الملف الشخصي** (`profile.html`)
- المعلومات الشخصية
- إعدادات الأمان
- إدارة العناوين
- التفضيلات
- الإشعارات

### 📦 **الطلبات** (`orders.html`)
- عرض جميع الطلبات
- تتبع الطلبات
- فلترة حسب الحالة
- إدارة الطلبات

## التقنيات المستخدمة

### 🎨 **التصميم**
- **Tailwind CSS**: إطار عمل CSS للتصميم السريع
- **Google Fonts**: خطوط Cairo و Amiri للنصوص العربية
- **CSS Animations**: تأثيرات حركية مخصصة

### 💻 **البرمجة**
- **HTML5**: هيكل الصفحات
- **JavaScript ES6+**: الوظائف التفاعلية
- **CSS3**: التصميم والتأثيرات

### 🌐 **المميزات**
- **Responsive Design**: تصميم متجاوب
- **Progressive Enhancement**: تحسين تدريجي
- **Accessibility**: إمكانية الوصول
- **SEO Optimized**: محسن لمحركات البحث

## كيفية الاستخدام

### 1. **فتح الموقع**
```bash
# افتح index.html في المتصفح
open index.html
```

### 2. **التنقل**
- استخدم القائمة الرئيسية للتنقل بين الصفحات
- استخدم شريط البحث للبحث عن المنتجات
- استخدم الفلاتر لتصفية النتائج

### 3. **تغيير اللغة**
- انقر على قائمة اللغة في الهيدر
- اختر اللغة المطلوبة
- سيتم تحديث جميع النصوص تلقائياً

### 4. **التسوق**
- تصفح المنتجات في صفحة المنتجات
- أضف المنتجات للسلة أو المفضلة
- راجع السلة وأكمل عملية الشراء

## الوظائف التفاعلية

### 🔍 **البحث والفلترة**
```javascript
// البحث في المنتجات
searchProducts(query)

// فلترة حسب الفئة
filterByCategory(category)

// ترتيب النتائج
sortProducts(criteria)
```

### 🛒 **إدارة السلة**
```javascript
// إضافة منتج للسلة
addToCart(productId, quantity)

// تحديث الكمية
updateQuantity(productId, newQuantity)

// حذف من السلة
removeFromCart(productId)
```

### ❤️ **إدارة المفضلة**
```javascript
// إضافة للمفضلة
addToWishlist(productId)

// إزالة من المفضلة
removeFromWishlist(productId)

// إضافة الكل للسلة
addAllToCart()
```

### 🌐 **نظام الترجمة**
```javascript
// تغيير اللغة
changeLanguage(languageCode)

// ترجمة نص
t(key, fallback)

// تحديث النصوص
updateTranslations()
```

## التخصيص

### 🎨 **الألوان**
```css
:root {
  --pharaoh-gold: #D4AF37;
  --pharaoh-blue: #003366;
  --pharaoh-sand: #F4E4BC;
  --pharaoh-copper: #B87333;
}
```

### 📱 **نقاط التوقف**
```css
/* الهاتف */
@media (max-width: 640px) { }

/* التابلت */
@media (min-width: 768px) { }

/* سطح المكتب */
@media (min-width: 1024px) { }
```

## الأمان والأداء

### 🔒 **الأمان**
- تشفير البيانات الحساسة
- التحقق من صحة المدخلات
- حماية من XSS و CSRF
- جلسات آمنة

### ⚡ **الأداء**
- تحسين الصور
- ضغط الملفات
- تحميل تدريجي
- ذاكرة التخزين المؤقت

## المتطلبات

### 🌐 **المتصفحات المدعومة**
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 📱 **الأجهزة**
- الهواتف الذكية
- الأجهزة اللوحية
- أجهزة سطح المكتب
- الشاشات الكبيرة

## التطوير المستقبلي

### 🚀 **المميزات المخططة**
- [ ] تطبيق الهاتف المحمول
- [ ] دفع إلكتروني متقدم
- [ ] ذكاء اصطناعي للتوصيات
- [ ] تحليلات متقدمة
- [ ] API للمطورين

### 🔧 **التحسينات**
- [ ] تحسين الأداء
- [ ] مزيد من اللغات
- [ ] ميزات إضافية
- [ ] تحسين UX/UI

## الدعم

### 📧 **التواصل**
- البريد الإلكتروني: <EMAIL>
- الدعم الفني: <EMAIL>
- الهاتف: 19999

### 🕒 **ساعات العمل**
- الأحد - الخميس: 9:00 ص - 6:00 م
- الجمعة: 2:00 م - 6:00 م
- السبت: مغلق
- الدعم الطارئ: 24/7

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للمزيد من التفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال طلبات السحب.

---

**© 2024 مركز MSB. جميع الحقوق محفوظة.**

*One Brand, Every Solution* 🏛️
