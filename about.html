<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate="nav.about">من نحن - مركز MSB</title>
    <meta name="description" content="تعرف على مركز MSB - قصتنا، رؤيتنا، ومهمتنا في خدمة العملاء">
    <meta name="keywords" content="من نحن, مركز MSB, قصتنا, رؤيتنا, مهمتنا">

    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

    <!-- ملفات الترجمة والوظائف المشتركة -->
    <script src="js/translations.js"></script>
    <script src="js/common.js"></script>

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'pharaoh-gold': '#D4AF37',
                        'pharaoh-blue': '#003366',
                        'pharaoh-sand': '#F4E4BC',
                        'pharaoh-copper': '#B87333',
                    },
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                        'pharaoh': ['Amiri', 'serif'],
                    },
                    animation: {
                        'float': 'float 3s ease-in-out infinite',
                        'fade-in': 'fadeIn 0.8s ease-in-out',
                    }
                }
            }
        }
    </script>

    <style>
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .hieroglyph-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E");
        }

        .team-card {
            transition: all 0.3s ease;
        }

        .team-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .dark {
            background-color: #1a1a1a;
            color: #ffffff;
        }

        .dark .bg-white {
            background-color: #2d2d2d !important;
        }

        .dark .text-gray-600 {
            color: #a0a0a0 !important;
        }

        .dark .text-gray-700 {
            color: #b0b0b0 !important;
        }

        .dark .border-gray-200 {
            border-color: #404040 !important;
        }
    </style>
</head>
<body class="font-arabic">
    <!-- شريط العروض -->
    <div class="bg-pharaoh-gold text-pharaoh-blue py-2 text-center text-sm font-medium animate-pulse">
        <div class="container mx-auto px-4">
            <span data-translate="about.welcome_message">🏛️ اكتشف قصة مركز MSB - تراث مصري أصيل! 🏛️</span>
        </div>
    </div>

    <!-- الهيدر -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <!-- الهيدر الرئيسي -->
            <div class="flex items-center justify-between h-16">
                <!-- الشعار -->
                <div class="flex items-center space-x-2 space-x-reverse">
                    <div class="w-10 h-10 bg-pharaoh-gold rounded-lg flex items-center justify-center">
                        <span class="text-pharaoh-blue font-bold text-xl">M</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-pharaoh text-pharaoh-blue" data-translate="site.title">مركز MSB</h1>
                        <p class="text-xs text-gray-600" data-translate="site.tagline">One Brand, Every Solution</p>
                    </div>
                </div>

                <!-- شريط البحث -->
                <div class="hidden lg:flex flex-1 max-w-xl mx-8">
                    <div class="relative w-full">
                        <input type="text" data-translate="search.placeholder" placeholder="ابحث عن المنتجات والخدمات..."
                               class="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                        <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            🔍
                        </div>
                    </div>
                </div>

                <!-- أدوات الهيدر -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button id="themeToggle" class="p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="theme.toggle" title="تبديل الثيم">🌙</button>

                    <!-- قائمة اللغات -->
                    <div class="relative">
                        <button id="languageToggle" class="flex items-center p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="language.change" title="تغيير اللغة">
                            <span id="currentFlag">🇪🇬</span>
                            <span class="ml-1 text-sm font-medium" id="currentLang">العربية</span>
                        </button>
                        <div id="languageMenu" class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border hidden z-50">
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors language-option" data-lang="ar" data-flag="🇪🇬" data-name="العربية">
                                <span class="mr-2">🇪🇬</span>
                                <span data-translate="language.arabic">العربية</span>
                                <span class="mr-auto text-green-500" id="check-ar">✓</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors language-option" data-lang="en" data-flag="🇺🇸" data-name="English">
                                <span class="mr-2">🇺🇸</span>
                                <span data-translate="language.english">English</span>
                                <span class="mr-auto text-green-500 hidden" id="check-en">✓</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors language-option" data-lang="fr" data-flag="🇫🇷" data-name="Français">
                                <span class="mr-2">🇫🇷</span>
                                <span data-translate="language.french">Français</span>
                                <span class="mr-auto text-green-500 hidden" id="check-fr">✓</span>
                            </a>
                        </div>
                    </div>

                    <button class="relative p-2 rounded-lg hover:bg-gray-100 transition-colors wishlist-counter" data-translate-title="nav.wishlist" title="المفضلة" onclick="window.location.href='wishlist.html'">
                        ❤️
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-pharaoh-gold text-pharaoh-blue text-xs rounded-full flex items-center justify-center font-bold">3</span>
                    </button>
                    <button class="relative p-2 rounded-lg hover:bg-gray-100 transition-colors cart-counter" data-translate-title="nav.cart" title="سلة التسوق" onclick="window.location.href='cart.html'">
                        🛒
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-pharaoh-gold text-pharaoh-blue text-xs rounded-full flex items-center justify-center font-bold">2</span>
                    </button>

                    <!-- قائمة المستخدم -->
                    <div class="relative">
                        <button id="userToggle" class="p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="nav.account" title="حسابي">👤</button>
                        <div id="userMenu" class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border hidden z-50">
                            <div class="px-4 py-2 border-b border-gray-200">
                                <p class="text-sm font-medium text-gray-900">أحمد محمد</p>
                                <p class="text-xs text-gray-500"><EMAIL></p>
                            </div>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="profile">
                                <span class="mr-2">👤</span>
                                <span data-translate="nav.account">حسابي</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="orders">
                                <span class="mr-2">📦</span>
                                <span data-translate="nav.orders">طلباتي</span>
                                <span class="mr-auto bg-pharaoh-gold text-pharaoh-blue text-xs px-2 py-1 rounded-full">3</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="wishlist">
                                <span class="mr-2">❤️</span>
                                <span data-translate="nav.wishlist">المفضلة</span>
                                <span class="mr-auto bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">5</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="settings">
                                <span class="mr-2">⚙️</span>
                                <span data-translate="nav.settings">الإعدادات</span>
                            </a>
                            <hr class="my-1">
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-red-50 transition-colors text-red-600 user-option" data-action="logout">
                                <span class="mr-2">🚪</span>
                                <span data-translate="nav.logout">تسجيل الخروج</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التنقل الرئيسي -->
            <nav class="hidden lg:flex items-center justify-center py-4 border-t border-gray-200">
                <div class="flex items-center space-x-8 space-x-reverse">
                    <a href="index.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.home">الرئيسية</a>
                    <a href="products.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.products">المنتجات</a>
                    <a href="services.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.services">الخدمات</a>
                    <a href="about.html" class="text-pharaoh-blue font-bold transition-colors" data-translate="nav.about">من نحن</a>
                    <a href="contact.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.contact">اتصل بنا</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- شريط التنقل -->
    <div class="bg-pharaoh-blue text-white py-3">
        <div class="container mx-auto px-4">
            <div class="flex items-center space-x-2 space-x-reverse text-sm">
                <a href="index.html" class="hover:text-pharaoh-gold" data-translate="nav.home">الرئيسية</a>
                <span>←</span>
                <span class="text-pharaoh-gold" data-translate="nav.about">من نحن</span>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <main class="min-h-screen">
        <!-- قسم البطل -->
        <section class="relative h-96 bg-gradient-to-r from-pharaoh-blue to-pharaoh-gold text-white overflow-hidden">
            <div class="absolute inset-0 hieroglyph-pattern opacity-10"></div>
            <div class="relative h-full flex items-center">
                <div class="container mx-auto px-4">
                    <div class="text-center space-y-6">
                        <div class="inline-block bg-pharaoh-gold text-pharaoh-blue px-4 py-2 rounded-full text-sm font-bold animate-pulse">
                            <span data-translate="about.heritage">تراث مصري أصيل</span>
                        </div>
                        <h1 class="text-4xl md:text-5xl font-pharaoh leading-tight" data-translate="about.title">
                            قصة مركز MSB
                        </h1>
                        <p class="text-xl text-white/90 max-w-3xl mx-auto" data-translate="about.subtitle">
                            رحلة بدأت من حلم مصري أصيل لتصبح واقعاً يخدم ملايين العملاء في جميع أنحاء مصر
                        </p>
                        <div class="flex items-center justify-center space-x-8 space-x-reverse pt-6">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-pharaoh-gold">2020</div>
                                <div class="text-sm text-white/70" data-translate="about.founded">تأسس في</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-pharaoh-gold">100K+</div>
                                <div class="text-sm text-white/70" data-translate="about.customers">عميل راضي</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-pharaoh-gold">27</div>
                                <div class="text-sm text-white/70" data-translate="about.governorates">محافظة</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم قصتنا -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div class="space-y-6">
                        <h2 class="text-3xl md:text-4xl font-pharaoh text-pharaoh-blue" data-translate="about.our_story">
                            قصتنا
                        </h2>
                        <p class="text-gray-600 leading-relaxed" data-translate="about.story_text1">
                            بدأت قصة مركز MSB في عام 2020 كحلم بسيط: إنشاء منصة تجارة إلكترونية تجمع بين التراث المصري الأصيل والتكنولوجيا الحديثة.
                            أردنا أن نقدم للعملاء المصريين تجربة تسوق فريدة تعكس هويتنا الثقافية العريقة.
                        </p>
                        <p class="text-gray-600 leading-relaxed" data-translate="about.story_text2">
                            من خلال الجمع بين جودة المنتجات العالمية والخدمة المصرية الأصيلة، نجحنا في بناء جسر من الثقة مع عملائنا.
                            اليوم، نفخر بخدمة أكثر من 100,000 عميل في جميع أنحاء مصر.
                        </p>
                        <div class="flex space-x-4 space-x-reverse">
                            <button class="bg-pharaoh-gold text-pharaoh-blue px-6 py-3 rounded-lg font-bold hover:bg-yellow-300 transition-colors" onclick="window.location.href='products.html'">
                                <span data-translate="btn.shop_now">تسوق الآن</span>
                            </button>
                            <button class="border border-pharaoh-blue text-pharaoh-blue px-6 py-3 rounded-lg font-bold hover:bg-pharaoh-blue hover:text-white transition-colors" onclick="window.location.href='contact.html'">
                                <span data-translate="btn.contact_us">اتصل بنا</span>
                            </button>
                        </div>
                    </div>
                    <div class="relative">
                        <div class="w-full h-96 bg-gradient-to-br from-pharaoh-sand to-pharaoh-copper rounded-lg flex items-center justify-center">
                            <span class="text-6xl animate-float">🏛️</span>
                        </div>
                        <div class="absolute -bottom-4 -right-4 w-24 h-24 bg-pharaoh-gold rounded-full flex items-center justify-center">
                            <span class="text-2xl text-pharaoh-blue">📈</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم الرؤية والمهمة -->
        <section class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-pharaoh text-pharaoh-blue mb-4" data-translate="about.vision_mission">
                        رؤيتنا ومهمتنا
                    </h2>
                    <p class="text-gray-600 max-w-2xl mx-auto" data-translate="about.vision_mission_desc">
                        نسعى لأن نكون الخيار الأول للتجارة الإلكترونية في مصر والشرق الأوسط
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- الرؤية -->
                    <div class="bg-white rounded-lg shadow-lg p-8 text-center">
                        <div class="w-16 h-16 bg-pharaoh-gold rounded-full flex items-center justify-center mx-auto mb-6">
                            <span class="text-2xl text-pharaoh-blue">👁️</span>
                        </div>
                        <h3 class="text-2xl font-bold text-pharaoh-blue mb-4" data-translate="about.vision">
                            رؤيتنا
                        </h3>
                        <p class="text-gray-600 leading-relaxed" data-translate="about.vision_text">
                            أن نصبح المنصة الرائدة للتجارة الإلكترونية في مصر والشرق الأوسط،
                            ونقدم تجربة تسوق استثنائية تجمع بين التراث والحداثة،
                            مع الحفاظ على أعلى معايير الجودة والخدمة.
                        </p>
                    </div>

                    <!-- المهمة -->
                    <div class="bg-white rounded-lg shadow-lg p-8 text-center">
                        <div class="w-16 h-16 bg-pharaoh-gold rounded-full flex items-center justify-center mx-auto mb-6">
                            <span class="text-2xl text-pharaoh-blue">🎯</span>
                        </div>
                        <h3 class="text-2xl font-bold text-pharaoh-blue mb-4" data-translate="about.mission">
                            مهمتنا
                        </h3>
                        <p class="text-gray-600 leading-relaxed" data-translate="about.mission_text">
                            تقديم أفضل المنتجات والخدمات لعملائنا بأسعار تنافسية،
                            مع ضمان تجربة تسوق سهلة وآمنة،
                            والمساهمة في تطوير التجارة الإلكترونية في مصر.
                        </p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- الفوتر -->
    <footer class="bg-pharaoh-blue text-white">
        <!-- المحتوى الرئيسي -->
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- معلومات الشركة -->
                <div class="space-y-6">
                    <div>
                        <div class="flex items-center space-x-2 space-x-reverse mb-4">
                            <div class="w-12 h-12 bg-pharaoh-gold rounded-lg flex items-center justify-center">
                                <span class="text-pharaoh-blue font-bold text-xl">M</span>
                            </div>
                            <div>
                                <h3 class="text-xl font-pharaoh text-white" data-translate="site.title">مركز MSB</h3>
                                <p class="text-sm text-pharaoh-gold" data-translate="site.tagline">One Brand, Every Solution</p>
                            </div>
                        </div>
                        <p class="text-gray-400 text-sm leading-relaxed" data-translate="footer.about_desc">
                            مركز MSB هو وجهتك الأولى للتجارة الإلكترونية في مصر. نقدم أفضل المنتجات والخدمات
                            بجودة عالية وأسعار تنافسية مع خدمة عملاء متميزة.
                        </p>
                    </div>
                </div>

                <!-- روابط سريعة -->
                <div>
                    <h4 class="text-lg font-bold mb-6 text-pharaoh-gold" data-translate="footer.quick_links">روابط سريعة</h4>
                    <ul class="space-y-3">
                        <li><a href="index.html" class="text-gray-400 hover:text-pharaoh-gold transition-colors" data-translate="nav.home">الرئيسية</a></li>
                        <li><a href="products.html" class="text-gray-400 hover:text-pharaoh-gold transition-colors" data-translate="nav.products">المنتجات</a></li>
                        <li><a href="services.html" class="text-gray-400 hover:text-pharaoh-gold transition-colors" data-translate="nav.services">الخدمات</a></li>
                        <li><a href="about.html" class="text-gray-400 hover:text-pharaoh-gold transition-colors" data-translate="nav.about">من نحن</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-pharaoh-gold transition-colors" data-translate="nav.contact">اتصل بنا</a></li>
                    </ul>
                </div>

                <!-- الخدمات -->
                <div>
                    <h4 class="text-lg font-bold mb-6 text-pharaoh-gold" data-translate="services.title">خدماتنا</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors" data-translate="services.maintenance">الصيانة</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors" data-translate="services.delivery">التوصيل</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors" data-translate="services.installation">التركيب</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors" data-translate="services.support">الدعم الفني</a></li>
                    </ul>
                </div>

                <!-- معلومات الاتصال -->
                <div>
                    <h4 class="text-lg font-bold mb-6 text-pharaoh-gold" data-translate="footer.contact">تواصل معنا</h4>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <span class="text-pharaoh-gold">📍</span>
                            <span class="text-gray-400 text-sm">القاهرة، مصر</span>
                        </div>
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <span class="text-pharaoh-gold">📞</span>
                            <span class="text-gray-400 text-sm">19999</span>
                        </div>
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <span class="text-pharaoh-gold">📧</span>
                            <span class="text-gray-400 text-sm"><EMAIL></span>
                        </div>
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <span class="text-pharaoh-gold">🕒</span>
                            <span class="text-gray-400 text-sm" data-translate="services.available_24_7">متاح 24/7</span>
                        </div>
                    </div>

                    <!-- وسائل التواصل الاجتماعي -->
                    <div class="mt-6">
                        <h5 class="text-sm font-bold mb-3 text-pharaoh-gold" data-translate="footer.social_media">تابعنا</h5>
                        <div class="flex space-x-3 space-x-reverse">
                            <a href="#" class="w-8 h-8 bg-pharaoh-gold rounded-full flex items-center justify-center text-pharaoh-blue hover:bg-yellow-300 transition-colors">📘</a>
                            <a href="#" class="w-8 h-8 bg-pharaoh-gold rounded-full flex items-center justify-center text-pharaoh-blue hover:bg-yellow-300 transition-colors">📷</a>
                            <a href="#" class="w-8 h-8 bg-pharaoh-gold rounded-full flex items-center justify-center text-pharaoh-blue hover:bg-yellow-300 transition-colors">🐦</a>
                            <a href="#" class="w-8 h-8 bg-pharaoh-gold rounded-full flex items-center justify-center text-pharaoh-blue hover:bg-yellow-300 transition-colors">💼</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- حقوق النشر -->
        <div class="border-t border-pharaoh-gold/20 py-6">
            <div class="container mx-auto px-4">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm mb-4 md:mb-0">
                        © 2024 <span data-translate="site.title">مركز MSB</span>. <span data-translate="footer.rights">جميع الحقوق محفوظة</span>.
                    </p>
                    <div class="flex space-x-6 space-x-reverse text-sm">
                        <a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors" data-translate="footer.privacy">سياسة الخصوصية</a>
                        <a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors" data-translate="footer.terms">الشروط والأحكام</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // تم نقل جميع الوظائف إلى ملف common.js
        // النظام الجديد يدعم الترجمة الفورية والثيم المحسن
    </script>
</body>
</html>
