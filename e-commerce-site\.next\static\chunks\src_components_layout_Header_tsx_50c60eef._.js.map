{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder/New%20folder/e-commerce-site/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\r\nimport Link from 'next/link';\r\nimport { useTranslations } from 'next-intl';\r\n\r\nexport default function Header() {\r\n  const t = useTranslations('Header');\r\n  return (\r\n    <header className=\"bg-gray-900 text-white p-4\">\r\n      <div className=\"container mx-auto flex justify-between items-center\">\r\n        <Link href=\"/\" className=\"text-2xl font-bold text-yellow-400\">\r\n          MSB Center\r\n        </Link>\r\n        <nav>\r\n          <ul className=\"flex space-x-4\">\r\n            <li><Link href=\"/products\" className=\"hover:text-yellow-400\">{t('products')}</Link></li>\r\n            <li><Link href=\"/services\" className=\"hover:text-yellow-400\">{t('services')}</Link></li>\r\n            <li><Link href=\"/about\" className=\"hover:text-yellow-400\">{t('about')}</Link></li>\r\n            <li><Link href=\"/contact\" className=\"hover:text-yellow-400\">{t('contact')}</Link></li>\r\n          </ul>\r\n        </nav>\r\n        <div>\r\n          {/* Auth buttons will go here */}\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AAIe,SAAS;;IACtB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAI,WAAU;8BAAqC;;;;;;8BAG9D,6LAAC;8BACC,cAAA,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAyB,EAAE;;;;;;;;;;;0CAChE,6LAAC;0CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAyB,EAAE;;;;;;;;;;;0CAChE,6LAAC;0CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAyB,EAAE;;;;;;;;;;;0CAC7D,6LAAC;0CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAyB,EAAE;;;;;;;;;;;;;;;;;;;;;;8BAGnE,6LAAC;;;;;;;;;;;;;;;;AAMT;GAtBwB;;QACZ,yMAAA,CAAA,kBAAe;;;KADH", "debugId": null}}]}