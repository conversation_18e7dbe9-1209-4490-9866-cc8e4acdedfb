<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate="site.title">مركز MSB - One Brand, Every Solution</title>
    <meta name="description" content="موقع التجارة الإلكترونية المتكامل بالهوية الفرعونية المصرية الأصيلة - مركز MSB">
    <meta name="keywords" content="تجارة إلكترونية, مصر, فرعوني, تسوق, منتجات, خدمات, MSB">
    <meta name="author" content="مركز MSB">
    <meta name="robots" content="index, follow">
    <meta name="theme-color" content="#D4AF37">
    <meta name="msapplication-TileColor" content="#003366">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="مركز MSB">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Icons -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Crect width='32' height='32' rx='4' fill='%23D4AF37'/%3E%3Ctext x='16' y='20' text-anchor='middle' fill='%23003366' font-size='14' font-weight='bold'%3EM%3C/text%3E%3C/svg%3E">
    <link rel="apple-touch-icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='180' height='180' viewBox='0 0 180 180'%3E%3Crect width='180' height='180' rx='20' fill='%23D4AF37'/%3E%3Ctext x='90' y='110' text-anchor='middle' fill='%23003366' font-size='70' font-weight='bold'%3EM%3C/text%3E%3C/svg%3E">

    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

    <!-- ملفات الترجمة والوظائف المشتركة -->
    <script src="js/translations.js"></script>
    <script src="js/i18n-config.js"></script>
    <script src="js/language-switcher.js"></script>
    <script src="js/common.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'pharaoh-gold': '#D4AF37',
                        'pharaoh-blue': '#003366',
                        'pharaoh-sand': '#F4E4BC',
                        'pharaoh-copper': '#B87333',
                    },
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                        'pharaoh': ['Amiri', 'serif'],
                    },
                    animation: {
                        'float': 'float 3s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        @keyframes glow {
            0% { box-shadow: 0 0 5px #D4AF37; }
            100% { box-shadow: 0 0 20px #D4AF37, 0 0 30px #D4AF37; }
        }
        .hieroglyph-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E");
        }

        /* الوضع الليلي */
        .dark {
            background-color: #1a1a1a !important;
            color: #ffffff !important;
        }

        .dark header {
            background-color: #2d2d2d !important;
            border-bottom: 1px solid #444 !important;
        }

        .dark .bg-white {
            background-color: #2d2d2d !important;
            color: #ffffff !important;
        }

        .dark .text-gray-900 {
            color: #ffffff !important;
        }

        .dark .text-gray-700 {
            color: #cccccc !important;
        }

        .dark .text-gray-600 {
            color: #aaaaaa !important;
        }

        .dark .text-gray-500 {
            color: #999999 !important;
        }

        .dark .text-gray-400 {
            color: #888888 !important;
        }

        .dark .bg-gray-50 {
            background-color: #1a1a1a !important;
        }

        .dark .bg-gray-100 {
            background-color: #333333 !important;
        }

        .dark .bg-gray-200 {
            background-color: #404040 !important;
        }

        .dark .border-gray-200 {
            border-color: #404040 !important;
        }

        .dark .border-gray-300 {
            border-color: #555555 !important;
        }

        .dark .shadow-lg {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3) !important;
        }

        .dark .shadow-md {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2) !important;
        }

        .dark footer {
            background-color: #111111 !important;
        }

        .dark main {
            background-color: #1a1a1a !important;
        }

        .dark .card {
            background-color: #2d2d2d !important;
            border-color: #444444 !important;
        }

        .dark input {
            background-color: #333333 !important;
            border-color: #555555 !important;
            color: #ffffff !important;
        }

        .dark input::placeholder {
            color: #aaaaaa !important;
        }

        .dark button:hover {
            background-color: #404040 !important;
        }

        /* القوائم المنسدلة في الوضع الليلي */
        .dark #languageMenu,
        .dark #userMenu {
            background-color: #2d2d2d !important;
            border-color: #555555 !important;
            color: #ffffff !important;
        }

        .dark #languageMenu a:hover,
        .dark #userMenu a:hover {
            background-color: #404040 !important;
        }

        .dark .hover\\:bg-gray-100:hover {
            background-color: #404040 !important;
        }

        .dark .hover\\:bg-red-50:hover {
            background-color: #4a1a1a !important;
        }
    </style>
</head>
<body class="font-arabic">
    <!-- شريط العروض -->
    <div class="bg-pharaoh-gold text-pharaoh-blue py-2 text-center text-sm font-medium animate-pulse">
        <div class="container mx-auto px-4">
            <span data-translate="offer.special_offer">🎉 عروض خاصة - خصم يصل إلى 50% على جميع المنتجات! 🎉</span>
        </div>
    </div>

    <!-- الهيدر -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <!-- الهيدر الرئيسي -->
            <div class="flex items-center justify-between h-16">
                <!-- الشعار -->
                <div class="flex items-center space-x-2 space-x-reverse">
                    <div class="w-10 h-10 bg-pharaoh-gold rounded-lg flex items-center justify-center">
                        <span class="text-pharaoh-blue font-bold text-xl">M</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-pharaoh text-pharaoh-blue" data-translate="site.title">مركز MSB</h1>
                        <p class="text-xs text-gray-600" data-translate="site.tagline">One Brand, Every Solution</p>
                    </div>
                </div>

                <!-- شريط البحث -->
                <div class="hidden lg:flex flex-1 max-w-xl mx-8">
                    <div class="relative w-full">
                        <input type="text" data-translate="search.placeholder" placeholder="ابحث عن المنتجات والخدمات..."
                               class="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                        <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            🔍
                        </div>
                    </div>
                </div>

                <!-- أدوات الهيدر -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button id="themeToggle" class="p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="theme.toggle" title="تبديل الثيم">🌙</button>

                    <!-- قائمة اللغات المتقدمة -->
                    <div id="languageSwitcher" data-language-switcher class="relative">
                        <!-- سيتم إنشاء المحتوى بواسطة JavaScript -->
                    </div>

                    <button class="relative p-2 rounded-lg hover:bg-gray-100 transition-colors wishlist-counter" data-translate-title="nav.wishlist" title="المفضلة" onclick="window.location.href='wishlist.html'">
                        ❤️
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-pharaoh-gold text-pharaoh-blue text-xs rounded-full flex items-center justify-center font-bold">3</span>
                    </button>
                    <button class="relative p-2 rounded-lg hover:bg-gray-100 transition-colors cart-counter" data-translate-title="nav.cart" title="سلة التسوق" onclick="window.location.href='cart.html'">
                        🛒
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-pharaoh-gold text-pharaoh-blue text-xs rounded-full flex items-center justify-center font-bold">2</span>
                    </button>

                    <!-- قائمة المستخدم -->
                    <div class="relative">
                        <button id="userToggle" class="p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="nav.account" title="حسابي">👤</button>
                        <div id="userMenu" class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border hidden z-50">
                            <div class="px-4 py-2 border-b border-gray-200">
                                <p class="text-sm font-medium text-gray-900">أحمد محمد</p>
                                <p class="text-xs text-gray-500"><EMAIL></p>
                            </div>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="profile">
                                <span class="mr-2">👤</span>
                                <span data-translate="nav.account">حسابي</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="orders">
                                <span class="mr-2">📦</span>
                                <span data-translate="nav.orders">طلباتي</span>
                                <span class="mr-auto bg-pharaoh-gold text-pharaoh-blue text-xs px-2 py-1 rounded-full">3</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="wishlist">
                                <span class="mr-2">❤️</span>
                                <span data-translate="nav.wishlist">المفضلة</span>
                                <span class="mr-auto bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">5</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="settings">
                                <span class="mr-2">⚙️</span>
                                <span data-translate="nav.settings">الإعدادات</span>
                            </a>
                            <hr class="my-1">
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-red-50 transition-colors text-red-600 user-option" data-action="logout">
                                <span class="mr-2">🚪</span>
                                <span data-translate="nav.logout">تسجيل الخروج</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التنقل الرئيسي -->
            <nav class="hidden lg:flex items-center justify-center py-4 border-t border-gray-200">
                <div class="flex items-center space-x-8 space-x-reverse">
                    <a href="index.html" class="text-pharaoh-blue font-bold transition-colors" data-translate="nav.home">الرئيسية</a>
                    <a href="products.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.products">المنتجات</a>
                    <a href="services.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.services">الخدمات</a>
                    <a href="about.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.about">من نحن</a>
                    <a href="contact.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.contact">اتصل بنا</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main>
        <!-- قسم البطل -->
        <section class="relative h-96 md:h-[500px] bg-gradient-to-r from-pharaoh-blue to-pharaoh-gold text-white overflow-hidden">
            <div class="absolute inset-0 hieroglyph-pattern opacity-10"></div>
            <div class="relative h-full flex items-center">
                <div class="container mx-auto px-4">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                        <div class="text-center lg:text-right space-y-6">
                            <div class="inline-block bg-pharaoh-gold text-pharaoh-blue px-4 py-2 rounded-full text-sm font-bold">
                                <span data-translate="offer.discount">خصم 50%</span>
                            </div>
                            <h1 class="text-4xl md:text-5xl font-pharaoh leading-tight" data-translate="site.welcome">
                                مرحباً بك في مركز MSB
                            </h1>
                            <h2 class="text-xl md:text-2xl font-medium text-white/90" data-translate="hero.subtitle">
                                أفضل المنتجات والخدمات في مصر
                            </h2>
                            <p class="text-lg text-white/80 leading-relaxed" data-translate="site.description">
                                اكتشف مجموعة واسعة من المنتجات عالية الجودة والخدمات المتميزة تحت سقف واحد
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                                <button class="bg-pharaoh-gold text-pharaoh-blue px-8 py-4 rounded-lg font-bold text-lg hover:bg-yellow-300 transition-all duration-300 shadow-lg" onclick="window.location.href='products.html'">
                                    🛒 <span data-translate="btn.shop_now">تسوق الآن</span>
                                </button>
                                <button class="bg-white/20 backdrop-blur-sm text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-white/30 transition-all duration-300 border border-white/30" onclick="window.location.href='services.html'">
                                    🔧 <span data-translate="services.request">اطلب خدمة</span>
                                </button>
                            </div>
                            <div class="flex items-center space-x-8 space-x-reverse pt-6 justify-center lg:justify-start">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-pharaoh-gold">1000+</div>
                                    <div class="text-sm text-white/70" data-translate="products.title">منتج</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-pharaoh-gold">27</div>
                                    <div class="text-sm text-white/70" data-translate="contact.city">محافظة</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-pharaoh-gold">24/7</div>
                                    <div class="text-sm text-white/70" data-translate="services.support">دعم</div>
                                </div>
                            </div>
                        </div>
                        <div class="hidden lg:block">
                            <div class="relative w-full h-80 rounded-2xl overflow-hidden shadow-2xl">
                                <div class="w-full h-full bg-white/10 backdrop-blur-sm rounded-2xl flex items-center justify-center">
                                    <div class="text-center text-white">
                                        <div class="w-24 h-24 bg-pharaoh-gold rounded-full flex items-center justify-center mx-auto mb-4">
                                            <span class="text-4xl">🏺</span>
                                        </div>
                                        <h3 class="text-xl font-bold mb-2">الهوية الفرعونية</h3>
                                        <p class="text-white/80">تصميم مستوحى من التراث المصري</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم العروض الحصرية -->
        <section class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-pharaoh text-gray-900 mb-4">العروض الحصرية</h2>
                    <p class="text-lg text-gray-600">اكتشف أفضل العروض والخصومات المتاحة لفترة محدودة</p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="bg-gradient-to-br from-red-500 to-pink-500 rounded-2xl p-8 text-white relative overflow-hidden">
                        <div class="relative z-10">
                            <div class="flex items-center justify-between mb-6">
                                <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                                    <span class="text-2xl">⚡</span>
                                </div>
                                <div class="text-left">
                                    <div class="text-3xl font-bold">50%</div>
                                    <div class="text-sm opacity-80">خصم</div>
                                </div>
                            </div>
                            <h3 class="text-xl font-bold mb-3">خصم 50% على الإلكترونيات</h3>
                            <p class="text-white/90 text-sm mb-6">خصم كبير على جميع الأجهزة الإلكترونية</p>
                            <div class="flex items-center space-x-2 space-x-reverse mb-6">
                                <span class="text-sm">⏰ متبقي: 2 أيام</span>
                            </div>
                            <button class="w-full bg-white/20 backdrop-blur-sm border border-white/30 text-white py-3 rounded-lg font-medium hover:bg-white/30 transition-all">
                                استفد من العرض
                            </button>
                        </div>
                    </div>
                    <div class="bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl p-8 text-white">
                        <div class="flex items-center justify-between mb-6">
                            <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                                <span class="text-2xl">🎁</span>
                            </div>
                            <div class="text-left">
                                <div class="text-2xl font-bold">مجاني</div>
                                <div class="text-sm opacity-80">شحن</div>
                            </div>
                        </div>
                        <h3 class="text-xl font-bold mb-3">شحن مجاني</h3>
                        <p class="text-white/90 text-sm mb-6">شحن مجاني لجميع الطلبات أكثر من 500 جنيه</p>
                        <div class="flex items-center space-x-2 space-x-reverse mb-6">
                            <span class="text-sm">⏰ متبقي: دائم</span>
                        </div>
                        <button class="w-full bg-white/20 backdrop-blur-sm border border-white/30 text-white py-3 rounded-lg font-medium hover:bg-white/30 transition-all">
                            استفد من العرض
                        </button>
                    </div>
                    <div class="bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl p-8 text-white">
                        <div class="flex items-center justify-between mb-6">
                            <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                                <span class="text-2xl">💰</span>
                            </div>
                            <div class="text-left">
                                <div class="text-3xl font-bold">30%</div>
                                <div class="text-sm opacity-80">خصم</div>
                            </div>
                        </div>
                        <h3 class="text-xl font-bold mb-3">عروض الجملة</h3>
                        <p class="text-white/90 text-sm mb-6">أسعار خاصة للتجار والكميات الكبيرة</p>
                        <div class="flex items-center space-x-2 space-x-reverse mb-6">
                            <span class="text-sm">⏰ متبقي: 5 أيام</span>
                        </div>
                        <button class="w-full bg-white/20 backdrop-blur-sm border border-white/30 text-white py-3 rounded-lg font-medium hover:bg-white/30 transition-all">
                            استفد من العرض
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم المنتجات المميزة -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-pharaoh text-gray-900 mb-4">المنتجات المميزة</h2>
                    <p class="text-lg text-gray-600">اكتشف مجموعة مختارة من أفضل المنتجات عالية الجودة</p>
                </div>

                <!-- فلاتر الفئات -->
                <div class="flex flex-wrap justify-center gap-4 mb-12">
                    <button class="px-6 py-3 rounded-full font-medium bg-pharaoh-gold text-pharaoh-blue shadow-lg">الكل (12)</button>
                    <button class="px-6 py-3 rounded-full font-medium bg-gray-100 text-gray-700 hover:bg-gray-200">إلكترونيات (5)</button>
                    <button class="px-6 py-3 rounded-full font-medium bg-gray-100 text-gray-700 hover:bg-gray-200">المنزل (3)</button>
                    <button class="px-6 py-3 rounded-full font-medium bg-gray-100 text-gray-700 hover:bg-gray-200">أزياء (2)</button>
                    <button class="px-6 py-3 rounded-full font-medium bg-gray-100 text-gray-700 hover:bg-gray-200">رياضة (2)</button>
                </div>

                <!-- شبكة المنتجات -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- منتج 1 -->
                    <div class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
                        <div class="relative h-64 overflow-hidden">
                            <div class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10">-16%</div>
                            <div class="absolute top-4 left-4 bg-pharaoh-gold text-pharaoh-blue px-3 py-1 rounded-full text-sm font-bold z-10">الأكثر مبيعاً</div>
                            <div class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                                <div class="text-center text-gray-500">
                                    <div class="text-4xl mb-2">📱</div>
                                    <p class="text-sm">هاتف ذكي متطور</p>
                                </div>
                            </div>
                            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-gray-700 hover:bg-pharaoh-gold hover:text-pharaoh-blue transition-all">👁️</button>
                                    <button class="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-gray-700 hover:bg-red-500 hover:text-white transition-all">❤️</button>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center space-x-2 space-x-reverse mb-3">
                                <div class="flex items-center">
                                    <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                                </div>
                                <span class="text-sm text-gray-600">4.8 (124 تقييم)</span>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-2">هاتف ذكي متطور</h3>
                            <div class="flex flex-wrap gap-1 mb-4">
                                <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">شاشة OLED</span>
                                <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">كاميرا 108MP</span>
                            </div>
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <span class="text-2xl font-bold text-pharaoh-blue">15,999 ج.م</span>
                                    <span class="text-sm text-gray-500 line-through">18,999 ج.م</span>
                                </div>
                            </div>
                            <button class="w-full py-3 rounded-lg font-medium bg-pharaoh-blue text-white hover:bg-blue-800 transition-all duration-300 flex items-center justify-center space-x-2 space-x-reverse">
                                <span>🛒</span>
                                <span>أضف للسلة</span>
                            </button>
                        </div>
                    </div>

                    <!-- منتج 2 -->
                    <div class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
                        <div class="relative h-64 overflow-hidden">
                            <div class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10">-13%</div>
                            <div class="absolute top-4 left-4 bg-pharaoh-gold text-pharaoh-blue px-3 py-1 rounded-full text-sm font-bold z-10">جديد</div>
                            <div class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                                <div class="text-center text-gray-500">
                                    <div class="text-4xl mb-2">💻</div>
                                    <p class="text-sm">لابتوب للألعاب</p>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center space-x-2 space-x-reverse mb-3">
                                <div class="flex items-center">
                                    <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                                </div>
                                <span class="text-sm text-gray-600">4.9 (89 تقييم)</span>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-2">لابتوب للألعاب</h3>
                            <div class="flex flex-wrap gap-1 mb-4">
                                <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">RTX 4060</span>
                                <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">Intel i7</span>
                            </div>
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <span class="text-2xl font-bold text-pharaoh-blue">45,999 ج.م</span>
                                    <span class="text-sm text-gray-500 line-through">52,999 ج.م</span>
                                </div>
                            </div>
                            <button class="w-full py-3 rounded-lg font-medium bg-pharaoh-blue text-white hover:bg-blue-800 transition-all duration-300 flex items-center justify-center space-x-2 space-x-reverse">
                                <span>🛒</span>
                                <span>أضف للسلة</span>
                            </button>
                        </div>
                    </div>

                    <!-- منتج 3 -->
                    <div class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
                        <div class="relative h-64 overflow-hidden">
                            <div class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10">-22%</div>
                            <div class="absolute top-4 left-4 bg-pharaoh-gold text-pharaoh-blue px-3 py-1 rounded-full text-sm font-bold z-10">عرض خاص</div>
                            <div class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                                <div class="text-center text-gray-500">
                                    <div class="text-4xl mb-2">🍳</div>
                                    <p class="text-sm">طقم أواني المطبخ</p>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center space-x-2 space-x-reverse mb-3">
                                <div class="flex items-center">
                                    <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                                </div>
                                <span class="text-sm text-gray-600">4.6 (67 تقييم)</span>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-2">طقم أواني المطبخ</h3>
                            <div class="flex flex-wrap gap-1 mb-4">
                                <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">ستانلس ستيل</span>
                                <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">12 قطعة</span>
                            </div>
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <span class="text-2xl font-bold text-pharaoh-blue">2,499 ج.م</span>
                                    <span class="text-sm text-gray-500 line-through">3,199 ج.م</span>
                                </div>
                            </div>
                            <button class="w-full py-3 rounded-lg font-medium bg-pharaoh-blue text-white hover:bg-blue-800 transition-all duration-300 flex items-center justify-center space-x-2 space-x-reverse">
                                <span>🛒</span>
                                <span>أضف للسلة</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- زر عرض المزيد -->
                <div class="text-center mt-12">
                    <button class="bg-pharaoh-gold text-pharaoh-blue px-8 py-4 rounded-lg font-bold hover:bg-yellow-300 transition-all duration-300 flex items-center space-x-2 space-x-reverse mx-auto">
                        <span>عرض جميع المنتجات</span>
                        <span>←</span>
                    </button>
                </div>
            </div>
        </section>

        <!-- قسم الخدمات -->
        <section class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-pharaoh text-gray-900 mb-4">خدماتنا المتميزة</h2>
                    <p class="text-lg text-gray-600">نقدم مجموعة شاملة من الخدمات المتخصصة لضمان رضاكم التام</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- خدمة الصيانة -->
                    <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 group">
                        <div class="w-2 h-full bg-gradient-to-b from-blue-500 to-cyan-500 absolute top-0 right-0 rounded-r-2xl"></div>
                        <div class="relative mb-6">
                            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mb-4">
                                <span class="text-white text-2xl">🔧</span>
                            </div>
                            <div class="flex items-center justify-between text-sm text-gray-500">
                                <span>من 150 ج.م</span>
                                <span>2-4 ساعات</span>
                            </div>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-4">خدمات الصيانة</h3>
                        <p class="text-gray-600 leading-relaxed mb-4">صيانة احترافية لجميع أنواع الأجهزة مع ضمان الجودة</p>
                        <ul class="space-y-2 mb-6">
                            <li class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
                                <div class="w-2 h-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"></div>
                                <span>فنيين معتمدين</span>
                            </li>
                            <li class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
                                <div class="w-2 h-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"></div>
                                <span>قطع غيار أصلية</span>
                            </li>
                            <li class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
                                <div class="w-2 h-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"></div>
                                <span>ضمان 6 أشهر</span>
                            </li>
                        </ul>
                        <button class="w-full bg-gradient-to-r from-blue-500 to-cyan-500 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300">
                            اطلب الخدمة
                        </button>
                    </div>

                    <!-- خدمة التوصيل -->
                    <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 group">
                        <div class="w-2 h-full bg-gradient-to-b from-green-500 to-emerald-500 absolute top-0 right-0 rounded-r-2xl"></div>
                        <div class="relative mb-6">
                            <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mb-4">
                                <span class="text-white text-2xl">🚚</span>
                            </div>
                            <div class="flex items-center justify-between text-sm text-gray-500">
                                <span>من 25 ج.م</span>
                                <span>1-3 أيام</span>
                            </div>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-4">التوصيل السريع</h3>
                        <p class="text-gray-600 leading-relaxed mb-4">خدمة توصيل سريعة وآمنة لجميع أنحاء الجمهورية</p>
                        <ul class="space-y-2 mb-6">
                            <li class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
                                <div class="w-2 h-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                                <span>توصيل خلال 24 ساعة</span>
                            </li>
                            <li class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
                                <div class="w-2 h-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                                <span>تتبع الطلب</span>
                            </li>
                            <li class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
                                <div class="w-2 h-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                                <span>تأمين الشحنة</span>
                            </li>
                        </ul>
                        <button class="w-full bg-gradient-to-r from-green-500 to-emerald-500 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300">
                            اطلب الخدمة
                        </button>
                    </div>

                    <!-- خدمة الضمان -->
                    <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 group">
                        <div class="w-2 h-full bg-gradient-to-b from-purple-500 to-pink-500 absolute top-0 right-0 rounded-r-2xl"></div>
                        <div class="relative mb-6">
                            <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-4">
                                <span class="text-white text-2xl">🛡️</span>
                            </div>
                            <div class="flex items-center justify-between text-sm text-gray-500">
                                <span>مجاني</span>
                                <span>حتى سنة</span>
                            </div>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-4">الضمان الشامل</h3>
                        <p class="text-gray-600 leading-relaxed mb-4">ضمان شامل على جميع المنتجات والخدمات</p>
                        <ul class="space-y-2 mb-6">
                            <li class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
                                <div class="w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
                                <span>ضمان سنة كاملة</span>
                            </li>
                            <li class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
                                <div class="w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
                                <span>استبدال فوري</span>
                            </li>
                            <li class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
                                <div class="w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
                                <span>صيانة مجانية</span>
                            </li>
                        </ul>
                        <button class="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300">
                            اطلب الخدمة
                        </button>
                    </div>
                </div>

                <!-- قسم الحجز السريع -->
                <div class="mt-16 bg-pharaoh-blue rounded-2xl p-8 text-center text-white relative overflow-hidden">
                    <div class="absolute inset-0 hieroglyph-pattern opacity-10"></div>
                    <div class="relative">
                        <h3 class="text-2xl font-pharaoh mb-4">هل تحتاج خدمة عاجلة؟</h3>
                        <p class="text-lg mb-6 text-white/90">احجز موعدك الآن واحصل على خدمة سريعة ومتخصصة من فريقنا المحترف</p>
                        <div class="max-w-md mx-auto">
                            <div class="flex flex-col sm:flex-row gap-4">
                                <input type="tel" placeholder="رقم الهاتف" class="flex-1 px-4 py-3 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/70 focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                                <button class="bg-pharaoh-gold text-pharaoh-blue px-6 py-3 rounded-lg font-bold hover:bg-yellow-300 transition-all duration-300">احجز الآن</button>
                            </div>
                            <p class="text-sm text-white/70 mt-3">سنتصل بك خلال 15 دقيقة لتأكيد الموعد</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- المساعد الذكي العائم -->
    <button class="fixed bottom-6 left-6 w-16 h-16 bg-pharaoh-gold rounded-full shadow-lg flex items-center justify-center z-50 hover:scale-110 transition-all duration-300 animate-float" onclick="toggleChat()">
        <span class="text-pharaoh-blue text-2xl">🤖</span>
        <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
            <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
        </div>
    </button>

    <!-- نافذة المحادثة -->
    <div id="chatWindow" class="fixed bottom-6 left-6 w-80 bg-white rounded-2xl shadow-2xl z-50 overflow-hidden border border-gray-200 hidden">
        <div class="bg-pharaoh-blue text-white p-4 flex items-center justify-between">
            <div class="flex items-center space-x-3 space-x-reverse">
                <div class="w-10 h-10 bg-pharaoh-gold rounded-full flex items-center justify-center">
                    <span class="text-pharaoh-blue">🤖</span>
                </div>
                <div>
                    <h3 class="font-bold">مساعد MSB الذكي</h3>
                    <p class="text-xs text-white/80">متاح الآن</p>
                </div>
            </div>
            <button onclick="toggleChat()" class="w-8 h-8 hover:bg-white/20 rounded-lg flex items-center justify-center transition-colors">
                ✕
            </button>
        </div>
        <div class="h-80 overflow-y-auto p-4 space-y-4">
            <div class="flex justify-start">
                <div class="flex items-start space-x-2 space-x-reverse max-w-[80%]">
                    <div class="w-8 h-8 bg-pharaoh-blue rounded-full flex items-center justify-center flex-shrink-0">
                        <span class="text-white text-sm">🤖</span>
                    </div>
                    <div class="bg-gray-100 rounded-2xl px-4 py-2">
                        <p class="text-sm">مرحباً بك في مركز MSB! 👋 كيف يمكنني مساعدتك اليوم؟</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="p-4 border-t border-gray-200">
            <div class="flex space-x-2 space-x-reverse">
                <input type="text" placeholder="اكتب رسالتك هنا..."
                       class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent text-sm">
                <button class="w-10 h-10 bg-pharaoh-gold text-pharaoh-blue rounded-lg flex items-center justify-center hover:bg-yellow-300 transition-colors">
                    ➤
                </button>
            </div>
        </div>
    </div>

    <!-- الفوتر -->
    <footer class="bg-gray-900 text-white">
        <!-- قسم الميزات -->
        <div class="border-b border-gray-800">
            <div class="container mx-auto px-4 py-8">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="flex-shrink-0 w-12 h-12 bg-pharaoh-gold rounded-lg flex items-center justify-center">
                            <span class="text-pharaoh-blue text-xl">🚚</span>
                        </div>
                        <p class="text-sm text-gray-300">شحن مجاني للطلبات أكثر من 500 جنيه</p>
                    </div>
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="flex-shrink-0 w-12 h-12 bg-pharaoh-gold rounded-lg flex items-center justify-center">
                            <span class="text-pharaoh-blue text-xl">🛡️</span>
                        </div>
                        <p class="text-sm text-gray-300">ضمان الجودة والأصالة</p>
                    </div>
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="flex-shrink-0 w-12 h-12 bg-pharaoh-gold rounded-lg flex items-center justify-center">
                            <span class="text-pharaoh-blue text-xl">💳</span>
                        </div>
                        <p class="text-sm text-gray-300">طرق دفع آمنة ومتنوعة</p>
                    </div>
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="flex-shrink-0 w-12 h-12 bg-pharaoh-gold rounded-lg flex items-center justify-center">
                            <span class="text-pharaoh-blue text-xl">🏆</span>
                        </div>
                        <p class="text-sm text-gray-300">خدمة عملاء متميزة 24/7</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- معلومات الشركة -->
                <div class="space-y-6">
                    <div>
                        <div class="flex items-center space-x-2 space-x-reverse mb-4">
                            <div class="w-12 h-12 bg-pharaoh-gold rounded-lg flex items-center justify-center">
                                <span class="text-pharaoh-blue font-bold text-xl">M</span>
                            </div>
                            <div>
                                <h3 class="text-xl font-pharaoh text-white">مركز MSB</h3>
                                <p class="text-sm text-pharaoh-gold">One Brand, Every Solution</p>
                            </div>
                        </div>
                        <p class="text-gray-400 text-sm leading-relaxed">
                            مركز MSB هو وجهتك الأولى للتجارة الإلكترونية في مصر. نقدم أفضل المنتجات والخدمات
                            بجودة عالية وأسعار تنافسية مع خدمة عملاء متميزة.
                        </p>
                    </div>

                    <!-- معلومات الاتصال -->
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <span class="text-pharaoh-gold">📞</span>
                            <span class="text-sm text-gray-300">+20 100 123 4567</span>
                        </div>
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <span class="text-pharaoh-gold">✉️</span>
                            <span class="text-sm text-gray-300"><EMAIL></span>
                        </div>
                        <div class="flex items-start space-x-3 space-x-reverse">
                            <span class="text-pharaoh-gold">📍</span>
                            <span class="text-sm text-gray-300">شارع التحرير، وسط البلد، القاهرة، مصر</span>
                        </div>
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <span class="text-pharaoh-gold">🕒</span>
                            <span class="text-sm text-gray-300">السبت - الخميس: 9:00 ص - 10:00 م</span>
                        </div>
                    </div>
                </div>

                <!-- روابط سريعة -->
                <div>
                    <h4 class="text-lg font-semibold mb-6 text-white">روابط سريعة</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">من نحن</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">سياسة الخصوصية</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">الشروط والأحكام</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">سياسة الشحن</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">سياسة الإرجاع</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">الأسئلة الشائعة</a></li>
                    </ul>
                </div>

                <!-- فئات المنتجات -->
                <div>
                    <h4 class="text-lg font-semibold mb-6 text-white">فئات المنتجات</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">الإلكترونيات</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">المنزل والحديقة</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">الأزياء</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">الرياضة</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">الكتب</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">السيارات</a></li>
                    </ul>
                </div>

                <!-- الخدمات -->
                <div>
                    <h4 class="text-lg font-semibold mb-6 text-white">خدماتنا</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">خدمات الصيانة</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">التركيب</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">الاستشارات</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">الضمان</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">الدعم الفني</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-pharaoh-gold transition-colors text-sm">التدريب</a></li>
                    </ul>
                </div>
            </div>

            <!-- النشرة الإخبارية -->
            <div class="mt-12 pt-8 border-t border-gray-800">
                <div class="max-w-md mx-auto text-center">
                    <h4 class="text-lg font-semibold mb-4 text-white">اشترك في النشرة الإخبارية</h4>
                    <p class="text-gray-400 text-sm mb-6">
                        احصل على آخر العروض والمنتجات الجديدة مباشرة في بريدك الإلكتروني
                    </p>
                    <div class="flex space-x-2 space-x-reverse">
                        <input type="email" placeholder="أدخل بريدك الإلكتروني"
                               class="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent text-white placeholder-gray-400">
                        <button class="px-6 py-2 bg-pharaoh-gold text-pharaoh-blue rounded-lg hover:bg-yellow-300 transition-colors font-medium">
                            اشتراك
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- الجزء السفلي -->
        <div class="border-t border-gray-800">
            <div class="container mx-auto px-4 py-6">
                <div class="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
                    <!-- حقوق النشر -->
                    <div class="text-center md:text-right">
                        <p class="text-gray-400 text-sm">© 2024 مركز MSB. جميع الحقوق محفوظة.</p>
                    </div>

                    <!-- وسائل التواصل الاجتماعي -->
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 hover:text-blue-600 transition-colors">📘</a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 hover:text-pink-600 transition-colors">📷</a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 hover:text-blue-400 transition-colors">🐦</a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 hover:text-red-600 transition-colors">📺</a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 hover:text-blue-700 transition-colors">💼</a>
                    </div>

                    <!-- طرق الدفع -->
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <span class="text-gray-400 text-sm">طرق الدفع:</span>
                        <div class="flex space-x-2 space-x-reverse">
                            <div class="w-8 h-6 bg-blue-600 rounded text-white text-xs flex items-center justify-center font-bold">VISA</div>
                            <div class="w-8 h-6 bg-red-600 rounded text-white text-xs flex items-center justify-center font-bold">MC</div>
                            <div class="w-8 h-6 bg-green-600 rounded text-white text-xs flex items-center justify-center font-bold">VF</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // تسجيل Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/service-worker.js')
                    .then(function(registration) {
                        console.log('✅ Service Worker مسجل بنجاح:', registration.scope);
                    })
                    .catch(function(error) {
                        console.log('❌ فشل تسجيل Service Worker:', error);
                    });
            });
        }

        // إضافة زر تثبيت التطبيق
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;

            // إظهار زر التثبيت
            const installButton = document.createElement('button');
            installButton.textContent = '📱 تثبيت التطبيق';
            installButton.className = 'fixed bottom-4 right-4 bg-pharaoh-gold text-pharaoh-blue px-4 py-2 rounded-lg font-bold shadow-lg hover:bg-yellow-300 transition-colors z-50';
            installButton.addEventListener('click', () => {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('✅ المستخدم قبل تثبيت التطبيق');
                    } else {
                        console.log('❌ المستخدم رفض تثبيت التطبيق');
                    }
                    deferredPrompt = null;
                    installButton.remove();
                });
            });
            document.body.appendChild(installButton);
        });

        // وظيفة تبديل المحادثة
        function toggleChat() {
            const chatWindow = document.getElementById('chatWindow');
            if (chatWindow) {
                chatWindow.classList.toggle('hidden');
            }
        }
    </script>
</body>
</html>
