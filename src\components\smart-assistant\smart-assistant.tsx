'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  MessageCircle, 
  X, 
  Send, 
  Bot, 
  User, 
  Minimize2, 
  Maximize2,
  Phone,
  Mail
} from 'lucide-react'
import { useLanguage } from '@/hooks/use-language'

interface Message {
  id: string
  text: string
  sender: 'user' | 'bot'
  timestamp: Date
  type?: 'text' | 'quick-reply' | 'product' | 'service'
}

export default function SmartAssistant() {
  const { t, isRTL } = useLanguage()
  const [isOpen, setIsOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'مرحباً بك في مركز MSB! 👋 كيف يمكنني مساعدتك اليوم؟',
      sender: 'bot',
      timestamp: new Date(),
      type: 'text'
    }
  ])
  const [inputText, setInputText] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const quickReplies = [
    'أريد معرفة أسعار المنتجات',
    'كيف يمكنني تتبع طلبي؟',
    'ما هي طرق الدفع المتاحة؟',
    'أحتاج مساعدة في الصيانة',
    'تحدث مع ممثل'
  ]

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async (text: string) => {
    if (!text.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      text: text.trim(),
      sender: 'user',
      timestamp: new Date(),
      type: 'text'
    }

    setMessages(prev => [...prev, userMessage])
    setInputText('')
    setIsTyping(true)

    // محاكاة الرد الآلي
    setTimeout(() => {
      const botResponse = generateBotResponse(text.trim())
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: botResponse,
        sender: 'bot',
        timestamp: new Date(),
        type: 'text'
      }
      setMessages(prev => [...prev, botMessage])
      setIsTyping(false)
    }, 1000 + Math.random() * 2000)
  }

  const generateBotResponse = (userText: string): string => {
    const lowerText = userText.toLowerCase()
    
    if (lowerText.includes('سعر') || lowerText.includes('أسعار')) {
      return 'يمكنك تصفح أسعار جميع المنتجات من خلال قسم المنتجات في الموقع. هل تبحث عن منتج معين؟'
    }
    
    if (lowerText.includes('تتبع') || lowerText.includes('طلب')) {
      return 'لتتبع طلبك، يرجى إدخال رقم الطلب في صفحة "تتبع الطلب" أو يمكنني مساعدتك إذا أعطيتني رقم الطلب.'
    }
    
    if (lowerText.includes('دفع') || lowerText.includes('الدفع')) {
      return 'نقبل طرق الدفع التالية: فودافون كاش، إنستاباي، الدفع عند الاستلام، والتحويل البنكي. أيها تفضل؟'
    }
    
    if (lowerText.includes('صيانة') || lowerText.includes('إصلاح')) {
      return 'نقدم خدمات صيانة متخصصة لجميع أنواع الأجهزة. يمكنك حجز موعد من خلال قسم الخدمات أو الاتصال بنا مباشرة.'
    }
    
    if (lowerText.includes('ممثل') || lowerText.includes('موظف')) {
      return 'سأقوم بتحويلك إلى أحد ممثلي خدمة العملاء. يرجى الانتظار قليلاً... 📞'
    }
    
    if (lowerText.includes('شحن') || lowerText.includes('توصيل')) {
      return 'نوفر خدمة توصيل لجميع محافظات مصر. الشحن مجاني للطلبات أكثر من 500 جنيه. متوسط وقت التوصيل 1-3 أيام.'
    }
    
    return 'شكراً لك على تواصلك معنا. يمكنني مساعدتك في الاستفسار عن المنتجات، الطلبات، الصيانة، أو أي شيء آخر. كيف يمكنني مساعدتك؟'
  }

  const handleQuickReply = (reply: string) => {
    handleSendMessage(reply)
  }

  return (
    <>
      {/* زر المساعد العائم */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className={`fixed bottom-6 right-6 rtl:right-auto rtl:left-6 w-16 h-16 bg-pharaoh-gold rounded-full shadow-lg flex items-center justify-center z-50 hover:scale-110 transition-all duration-300 ${isOpen ? 'hidden' : 'block'}`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        animate={{ 
          y: [0, -5, 0],
          boxShadow: [
            '0 4px 20px rgba(212, 175, 55, 0.3)',
            '0 8px 30px rgba(212, 175, 55, 0.5)',
            '0 4px 20px rgba(212, 175, 55, 0.3)'
          ]
        }}
        transition={{ 
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <MessageCircle className="w-8 h-8 text-pharaoh-blue" />
        
        {/* نقطة الإشعار */}
        <div className="absolute -top-1 -right-1 rtl:-right-auto rtl:-left-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
        </div>
      </motion.button>

      {/* نافذة المحادثة */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ 
              opacity: 1, 
              scale: isMinimized ? 0.3 : 1, 
              y: 0,
              height: isMinimized ? 60 : 500
            }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ duration: 0.3 }}
            className={`fixed bottom-6 right-6 rtl:right-auto rtl:left-6 w-80 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl z-50 overflow-hidden border border-gray-200 dark:border-gray-700 ${isMinimized ? 'cursor-pointer' : ''}`}
            onClick={isMinimized ? () => setIsMinimized(false) : undefined}
          >
            {/* الهيدر */}
            <div className="bg-pharaoh-blue text-white p-4 flex items-center justify-between">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-10 h-10 bg-pharaoh-gold rounded-full flex items-center justify-center">
                  <Bot className="w-6 h-6 text-pharaoh-blue" />
                </div>
                <div>
                  <h3 className="font-bold">مساعد MSB الذكي</h3>
                  <p className="text-xs text-white/80">متاح الآن</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    setIsMinimized(!isMinimized)
                  }}
                  className="w-8 h-8 hover:bg-white/20 rounded-lg flex items-center justify-center transition-colors"
                >
                  {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    setIsOpen(false)
                  }}
                  className="w-8 h-8 hover:bg-white/20 rounded-lg flex items-center justify-center transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>

            {!isMinimized && (
              <>
                {/* منطقة الرسائل */}
                <div className="h-80 overflow-y-auto p-4 space-y-4">
                  {messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`flex items-start space-x-2 rtl:space-x-reverse max-w-[80%] ${message.sender === 'user' ? 'flex-row-reverse rtl:flex-row' : ''}`}>
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                          message.sender === 'user' 
                            ? 'bg-pharaoh-gold' 
                            : 'bg-pharaoh-blue'
                        }`}>
                          {message.sender === 'user' ? (
                            <User className="w-4 h-4 text-pharaoh-blue" />
                          ) : (
                            <Bot className="w-4 h-4 text-white" />
                          )}
                        </div>
                        
                        <div className={`rounded-2xl px-4 py-2 ${
                          message.sender === 'user'
                            ? 'bg-pharaoh-gold text-pharaoh-blue'
                            : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                        }`}>
                          <p className="text-sm">{message.text}</p>
                          <p className="text-xs opacity-70 mt-1">
                            {message.timestamp.toLocaleTimeString('ar-EG', { 
                              hour: '2-digit', 
                              minute: '2-digit' 
                            })}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                  
                  {/* مؤشر الكتابة */}
                  {isTyping && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex justify-start"
                    >
                      <div className="flex items-start space-x-2 rtl:space-x-reverse">
                        <div className="w-8 h-8 bg-pharaoh-blue rounded-full flex items-center justify-center">
                          <Bot className="w-4 h-4 text-white" />
                        </div>
                        <div className="bg-gray-100 dark:bg-gray-700 rounded-2xl px-4 py-2">
                          <div className="flex space-x-1 rtl:space-x-reverse">
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                  
                  <div ref={messagesEndRef} />
                </div>

                {/* الردود السريعة */}
                {messages.length === 1 && (
                  <div className="px-4 pb-2">
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">اختر من الأسئلة الشائعة:</p>
                    <div className="flex flex-wrap gap-2">
                      {quickReplies.slice(0, 3).map((reply, index) => (
                        <button
                          key={index}
                          onClick={() => handleQuickReply(reply)}
                          className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full hover:bg-pharaoh-gold hover:text-pharaoh-blue transition-colors"
                        >
                          {reply}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* منطقة الإدخال */}
                <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <input
                      type="text"
                      value={inputText}
                      onChange={(e) => setInputText(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage(inputText)}
                      placeholder="اكتب رسالتك هنا..."
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                    />
                    <button
                      onClick={() => handleSendMessage(inputText)}
                      disabled={!inputText.trim()}
                      className="w-10 h-10 bg-pharaoh-gold text-pharaoh-blue rounded-lg flex items-center justify-center hover:bg-pharaoh-gold/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Send className="w-4 h-4" />
                    </button>
                  </div>
                  
                  {/* أزرار الاتصال السريع */}
                  <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                    <button className="flex items-center space-x-1 rtl:space-x-reverse text-xs text-pharaoh-blue dark:text-pharaoh-gold hover:underline">
                      <Phone className="w-3 h-3" />
                      <span>اتصل بنا</span>
                    </button>
                    <button className="flex items-center space-x-1 rtl:space-x-reverse text-xs text-pharaoh-blue dark:text-pharaoh-gold hover:underline">
                      <Mail className="w-3 h-3" />
                      <span>راسلنا</span>
                    </button>
                  </div>
                </div>
              </>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
