{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_2c2f7f9b._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_9e9e701a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(ar|en)/:path*{(\\\\.json)}?", "originalSource": "/(ar|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xOz67q10l7JPrsdMsfQnL3VENZvByFu6JuBkr3ZKlFo=", "__NEXT_PREVIEW_MODE_ID": "9602a52b0177ae2697bf523666a4c7f7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2411103d2756f070095a4d586458cf324e46e9de831971edaf39409ae223b5fa", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5da7c93bc08b6dd84f4127432cfebc9f56ef902700173683d692593559b2d299"}}}, "instrumentation": null, "functions": {}}