'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

type Language = 'ar' | 'en' | 'fr'

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
  isRTL: boolean
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

// ترجمات أساسية
const translations = {
  ar: {
    'site.title': 'مركز MSB',
    'site.tagline': 'One Brand, Every Solution',
    'site.description': 'كل ما تحتاجه تحت سقف واحد',
    'nav.home': 'الرئيسية',
    'nav.products': 'المنتجات',
    'nav.services': 'الخدمات',
    'nav.about': 'من نحن',
    'nav.contact': 'اتصل بنا',
    'nav.cart': 'السلة',
    'nav.account': 'حسابي',
    'nav.login': 'تسجيل الدخول',
    'nav.register': 'مستخدم جديد',
    'hero.title': 'مرحباً بك في مركز MSB',
    'hero.subtitle': 'أفضل المنتجات والخدمات في مصر',
    'hero.cta.shop': 'تسوق الآن',
    'hero.cta.services': 'اطلب خدمة',
    'search.placeholder': 'ابحث عن المنتجات والخدمات...',
    'loading': 'جاري التحميل...',
  },
  en: {
    'site.title': 'MSB Center',
    'site.tagline': 'One Brand, Every Solution',
    'site.description': 'Everything you need under one roof',
    'nav.home': 'Home',
    'nav.products': 'Products',
    'nav.services': 'Services',
    'nav.about': 'About Us',
    'nav.contact': 'Contact',
    'nav.cart': 'Cart',
    'nav.account': 'My Account',
    'nav.login': 'Login',
    'nav.register': 'Register',
    'hero.title': 'Welcome to MSB Center',
    'hero.subtitle': 'Best Products and Services in Egypt',
    'hero.cta.shop': 'Shop Now',
    'hero.cta.services': 'Request Service',
    'search.placeholder': 'Search for products and services...',
    'loading': 'Loading...',
  },
  fr: {
    'site.title': 'Centre MSB',
    'site.tagline': 'Une Marque, Toutes les Solutions',
    'site.description': 'Tout ce dont vous avez besoin sous un même toit',
    'nav.home': 'Accueil',
    'nav.products': 'Produits',
    'nav.services': 'Services',
    'nav.about': 'À Propos',
    'nav.contact': 'Contact',
    'nav.cart': 'Panier',
    'nav.account': 'Mon Compte',
    'nav.login': 'Connexion',
    'nav.register': 'S\'inscrire',
    'hero.title': 'Bienvenue au Centre MSB',
    'hero.subtitle': 'Meilleurs Produits et Services en Égypte',
    'hero.cta.shop': 'Acheter Maintenant',
    'hero.cta.services': 'Demander un Service',
    'search.placeholder': 'Rechercher des produits et services...',
    'loading': 'Chargement...',
  },
}

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>('ar')

  useEffect(() => {
    // تحميل اللغة المحفوظة من localStorage
    const savedLanguage = localStorage.getItem('msb-language') as Language
    if (savedLanguage && ['ar', 'en', 'fr'].includes(savedLanguage)) {
      setLanguage(savedLanguage)
    }
  }, [])

  useEffect(() => {
    // حفظ اللغة في localStorage
    localStorage.setItem('msb-language', language)
    
    // تحديث اتجاه الصفحة
    document.documentElement.lang = language
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr'
  }, [language])

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key
  }

  const isRTL = language === 'ar'

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, isRTL }}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}
