{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder/New%20folder/e-commerce-site/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder/New%20folder/e-commerce-site/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder/New%20folder/e-commerce-site/src/components/layout/Footer.tsx"], "sourcesContent": ["export default function Footer() {\r\n  return (\r\n    <footer className=\"bg-gray-900 text-white p-4 mt-8\">\r\n      <div className=\"container mx-auto text-center\">\r\n        <p>&copy; {new Date().getFullYear()} MSB Center. All rights reserved.</p>\r\n        <p className=\"text-sm text-gray-400\">\"MSB-One Brand .Every Solution\"</p>\r\n      </div>\r\n    </footer>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;wBAAE;wBAAQ,IAAI,OAAO,WAAW;wBAAG;;;;;;;8BACpC,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAI7C", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder/New%20folder/e-commerce-site/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport \"./globals.css\";\nimport Header from \"@/components/layout/Header\";\nimport Footer from \"@/components/layout/Footer\";\nimport {NextIntlClientProvider, useMessages} from 'next-intl';\n\nexport const metadata: Metadata = {\n  title: \"MSB Center\",\n  description: \"MSB-One Brand .Every Solution\",\n};\n\nexport default function RootLayout({\n  children,\n  params: {locale}\n}: Readonly<{\n  children: React.ReactNode;\n  params: {locale: string};\n}>) {\n  const messages = useMessages();\n\n  return (\n    <html lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'}>\n      <body className=\"flex flex-col min-h-screen\">\n        <NextIntlClientProvider locale={locale} messages={messages}>\n          <Header />\n          <main className=\"flex-grow container mx-auto p-4\">\n            {children}\n          </main>\n          <Footer />\n        </NextIntlClientProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EACR,QAAQ,EAAC,MAAM,EAAC,EAIhB;IACA,MAAM,WAAW,CAAA,GAAA,sOAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAK,MAAM;QAAQ,KAAK,WAAW,OAAO,QAAQ;kBACjD,cAAA,8OAAC;YAAK,WAAU;sBACd,cAAA,8OAAC,kQAAA,CAAA,yBAAsB;gBAAC,QAAQ;gBAAQ,UAAU;;kCAChD,8OAAC,sIAAA,CAAA,UAAM;;;;;kCACP,8OAAC;wBAAK,WAAU;kCACb;;;;;;kCAEH,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;;;;;;;;;;;AAKjB", "debugId": null}}]}