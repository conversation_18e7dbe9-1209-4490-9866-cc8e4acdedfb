/**
 * نظام الترجمة المتقدم لمركز MSB
 * Advanced Internationalization System for MSB Center
 * 
 * يدعم:
 * - تحميل الترجمات بشكل ديناميكي
 * - تخزين مؤقت للترجمات
 * - تحديد اللغة تلقائياً
 * - تنسيق التواريخ والأرقام
 * - دعم RTL/LTR
 */

class I18nManager {
    constructor() {
        this.currentLanguage = 'ar';
        this.fallbackLanguage = 'ar';
        this.translations = {};
        this.loadedLanguages = new Set();
        this.rtlLanguages = ['ar', 'he', 'fa'];
        this.dateFormats = {
            ar: { locale: 'ar-EG', calendar: 'gregory' },
            en: { locale: 'en-US', calendar: 'gregory' },
            fr: { locale: 'fr-FR', calendar: 'gregory' }
        };
        this.numberFormats = {
            ar: { locale: 'ar-EG', currency: 'EGP' },
            en: { locale: 'en-US', currency: 'USD' },
            fr: { locale: 'fr-FR', currency: 'EUR' }
        };
        
        this.init();
    }

    /**
     * تهيئة نظام الترجمة
     */
    async init() {
        // تحديد اللغة من التخزين المحلي أو المتصفح
        this.currentLanguage = this.detectLanguage();
        
        // تحميل الترجمات الأساسية
        await this.loadTranslations(this.currentLanguage);
        
        // تطبيق اللغة على الصفحة
        this.applyLanguage();
        
        // إعداد مراقب تغيير اللغة
        this.setupLanguageObserver();
    }

    /**
     * تحديد اللغة تلقائياً
     */
    detectLanguage() {
        // من التخزين المحلي
        const savedLanguage = localStorage.getItem('msb-language');
        if (savedLanguage && this.isValidLanguage(savedLanguage)) {
            return savedLanguage;
        }

        // من المتصفح
        const browserLanguage = navigator.language.split('-')[0];
        if (this.isValidLanguage(browserLanguage)) {
            return browserLanguage;
        }

        // من URL
        const urlParams = new URLSearchParams(window.location.search);
        const urlLanguage = urlParams.get('lang');
        if (urlLanguage && this.isValidLanguage(urlLanguage)) {
            return urlLanguage;
        }

        return this.fallbackLanguage;
    }

    /**
     * التحقق من صحة اللغة
     */
    isValidLanguage(lang) {
        return ['ar', 'en', 'fr'].includes(lang);
    }

    /**
     * تحميل ترجمات لغة معينة
     */
    async loadTranslations(language) {
        if (this.loadedLanguages.has(language)) {
            return;
        }

        try {
            // في التطبيق الحقيقي، يمكن تحميل الترجمات من API
            // هنا نستخدم الترجمات المحلية
            if (window.translations && window.translations[language]) {
                this.translations[language] = window.translations[language];
                this.loadedLanguages.add(language);
            }
        } catch (error) {
            console.error(`خطأ في تحميل ترجمات اللغة ${language}:`, error);
        }
    }

    /**
     * تغيير اللغة
     */
    async changeLanguage(newLanguage) {
        if (!this.isValidLanguage(newLanguage) || newLanguage === this.currentLanguage) {
            return;
        }

        // تحميل الترجمات إذا لم تكن محملة
        await this.loadTranslations(newLanguage);

        // تحديث اللغة الحالية
        this.currentLanguage = newLanguage;

        // حفظ في التخزين المحلي
        localStorage.setItem('msb-language', newLanguage);

        // تطبيق اللغة الجديدة
        this.applyLanguage();

        // إرسال حدث تغيير اللغة
        this.dispatchLanguageChangeEvent();
    }

    /**
     * تطبيق اللغة على الصفحة
     */
    applyLanguage() {
        const html = document.documentElement;
        
        // تحديث خصائص HTML
        html.lang = this.currentLanguage;
        html.dir = this.isRTL(this.currentLanguage) ? 'rtl' : 'ltr';

        // تحديث النصوص
        this.updateTexts();

        // تحديث التواريخ والأرقام
        this.updateFormats();

        // تحديث الخطوط
        this.updateFonts();
    }

    /**
     * التحقق من اتجاه اللغة
     */
    isRTL(language) {
        return this.rtlLanguages.includes(language);
    }

    /**
     * تحديث النصوص في الصفحة
     */
    updateTexts() {
        const elements = document.querySelectorAll('[data-translate]');
        
        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.translate(key);
            
            if (translation !== key) {
                element.textContent = translation;
            }
        });
    }

    /**
     * ترجمة نص
     */
    translate(key, params = {}) {
        const translation = this.getTranslation(key);
        return this.interpolate(translation, params);
    }

    /**
     * الحصول على الترجمة
     */
    getTranslation(key) {
        const currentLangTranslations = this.translations[this.currentLanguage] || {};
        const fallbackTranslations = this.translations[this.fallbackLanguage] || {};
        
        return currentLangTranslations[key] || fallbackTranslations[key] || key;
    }

    /**
     * استبدال المتغيرات في النص
     */
    interpolate(text, params) {
        return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
            return params[key] !== undefined ? params[key] : match;
        });
    }

    /**
     * تنسيق التاريخ
     */
    formatDate(date, options = {}) {
        const format = this.dateFormats[this.currentLanguage];
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };

        return new Intl.DateTimeFormat(
            format.locale,
            { ...defaultOptions, ...options }
        ).format(new Date(date));
    }

    /**
     * تنسيق الرقم
     */
    formatNumber(number, options = {}) {
        const format = this.numberFormats[this.currentLanguage];
        
        return new Intl.NumberFormat(
            format.locale,
            options
        ).format(number);
    }

    /**
     * تنسيق العملة
     */
    formatCurrency(amount, currency = null) {
        const format = this.numberFormats[this.currentLanguage];
        const currencyCode = currency || format.currency;

        return new Intl.NumberFormat(format.locale, {
            style: 'currency',
            currency: currencyCode
        }).format(amount);
    }

    /**
     * تحديث التنسيقات في الصفحة
     */
    updateFormats() {
        // تحديث التواريخ
        document.querySelectorAll('[data-format-date]').forEach(element => {
            const date = element.getAttribute('data-format-date');
            element.textContent = this.formatDate(date);
        });

        // تحديث الأرقام
        document.querySelectorAll('[data-format-number]').forEach(element => {
            const number = element.getAttribute('data-format-number');
            element.textContent = this.formatNumber(number);
        });

        // تحديث العملات
        document.querySelectorAll('[data-format-currency]').forEach(element => {
            const amount = element.getAttribute('data-format-currency');
            const currency = element.getAttribute('data-currency');
            element.textContent = this.formatCurrency(amount, currency);
        });
    }

    /**
     * تحديث الخطوط
     */
    updateFonts() {
        const body = document.body;
        
        // إزالة فئات الخطوط السابقة
        body.classList.remove('font-arabic', 'font-english', 'font-french');
        
        // إضافة فئة الخط الجديدة
        switch (this.currentLanguage) {
            case 'ar':
                body.classList.add('font-arabic');
                break;
            case 'en':
                body.classList.add('font-english');
                break;
            case 'fr':
                body.classList.add('font-french');
                break;
        }
    }

    /**
     * إعداد مراقب تغيير اللغة
     */
    setupLanguageObserver() {
        // مراقبة تغيير URL
        window.addEventListener('popstate', () => {
            const newLanguage = this.detectLanguage();
            if (newLanguage !== this.currentLanguage) {
                this.changeLanguage(newLanguage);
            }
        });
    }

    /**
     * إرسال حدث تغيير اللغة
     */
    dispatchLanguageChangeEvent() {
        const event = new CustomEvent('languageChanged', {
            detail: {
                language: this.currentLanguage,
                isRTL: this.isRTL(this.currentLanguage)
            }
        });
        
        document.dispatchEvent(event);
    }

    /**
     * الحصول على اللغة الحالية
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * الحصول على اللغات المتاحة
     */
    getAvailableLanguages() {
        return [
            { code: 'ar', name: 'العربية', nativeName: 'العربية', flag: '🇪🇬' },
            { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
            { code: 'fr', name: 'Français', nativeName: 'Français', flag: '🇫🇷' }
        ];
    }

    /**
     * تحديث الترجمات ديناميكياً
     */
    updateTranslations(language, newTranslations) {
        if (!this.translations[language]) {
            this.translations[language] = {};
        }
        
        Object.assign(this.translations[language], newTranslations);
        
        if (language === this.currentLanguage) {
            this.updateTexts();
        }
    }
}

// إنشاء مثيل عام لمدير الترجمة
window.i18nManager = new I18nManager();

// تصدير الوظائف للاستخدام العام
window.t = (key, params) => window.i18nManager.translate(key, params);
window.changeLanguage = (lang) => window.i18nManager.changeLanguage(lang);
window.formatDate = (date, options) => window.i18nManager.formatDate(date, options);
window.formatNumber = (number, options) => window.i18nManager.formatNumber(number, options);
window.formatCurrency = (amount, currency) => window.i18nManager.formatCurrency(amount, currency);

// تصدير المدير للاستخدام المتقدم
export default I18nManager;
