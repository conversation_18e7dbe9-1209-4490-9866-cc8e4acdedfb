'use client'

import { motion } from 'framer-motion'
import { Clock, Zap, Gift, Percent } from 'lucide-react'
import { useLanguage } from '@/hooks/use-language'

export default function OffersSection() {
  const { t, isRTL } = useLanguage()

  const offers = [
    {
      id: 1,
      title: 'خصم 50% على الإلكترونيات',
      description: 'خصم كبير على جميع الأجهزة الإلكترونية',
      discount: '50%',
      timeLeft: '2 أيام',
      icon: Zap,
      color: 'from-red-500 to-pink-500',
      image: '/offer-electronics.jpg'
    },
    {
      id: 2,
      title: 'شحن مجاني',
      description: 'شحن مجاني لجميع الطلبات أكثر من 500 جنيه',
      discount: 'مجاني',
      timeLeft: 'دائم',
      icon: Gift,
      color: 'from-green-500 to-emerald-500',
      image: '/offer-shipping.jpg'
    },
    {
      id: 3,
      title: 'عروض الجملة',
      description: 'أسعار خاصة للتجار والكميات الكبيرة',
      discount: '30%',
      timeLeft: '5 أيام',
      icon: Percent,
      color: 'from-blue-500 to-cyan-500',
      image: '/offer-wholesale.jpg'
    }
  ]

  return (
    <section className="py-16 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4">
        {/* العنوان */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-pharaoh text-gray-900 dark:text-white mb-4">
            العروض الحصرية
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            اكتشف أفضل العروض والخصومات المتاحة لفترة محدودة
          </p>
        </motion.div>

        {/* العروض */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {offers.map((offer, index) => (
            <motion.div
              key={offer.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              whileHover={{ y: -5 }}
              className="relative group"
            >
              <div className="relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                {/* الخلفية المتدرجة */}
                <div className={`absolute inset-0 bg-gradient-to-br ${offer.color}`}></div>
                
                {/* المحتوى */}
                <div className="relative p-8 text-white">
                  {/* الأيقونة */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                      <offer.icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="text-right rtl:text-left">
                      <div className="text-3xl font-bold">{offer.discount}</div>
                      <div className="text-sm opacity-80">خصم</div>
                    </div>
                  </div>

                  {/* النص */}
                  <div className="space-y-3">
                    <h3 className="text-xl font-bold">{offer.title}</h3>
                    <p className="text-white/90 text-sm leading-relaxed">
                      {offer.description}
                    </p>
                  </div>

                  {/* الوقت المتبقي */}
                  <div className="flex items-center space-x-2 rtl:space-x-reverse mt-6 pt-6 border-t border-white/20">
                    <Clock className="w-4 h-4" />
                    <span className="text-sm">متبقي: {offer.timeLeft}</span>
                  </div>

                  {/* زر العمل */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full mt-6 bg-white/20 backdrop-blur-sm border border-white/30 text-white py-3 rounded-lg font-medium hover:bg-white/30 transition-all duration-300"
                  >
                    استفد من العرض
                  </motion.button>
                </div>

                {/* تأثير الهوفر */}
                <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* عداد العروض الخاصة */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="mt-16 bg-pharaoh-blue rounded-2xl p-8 text-center text-white relative overflow-hidden"
        >
          {/* الخلفية المزخرفة */}
          <div className="absolute inset-0 hieroglyph-pattern opacity-10"></div>
          
          <div className="relative">
            <h3 className="text-2xl font-pharaoh mb-4">عرض خاص لفترة محدودة!</h3>
            <p className="text-lg mb-6 text-white/90">
              خصم إضافي 20% على جميع المنتجات عند الشراء بقيمة 1000 جنيه أو أكثر
            </p>
            
            {/* العداد التنازلي */}
            <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse mb-6">
              {[
                { label: 'أيام', value: '02' },
                { label: 'ساعات', value: '14' },
                { label: 'دقائق', value: '35' },
                { label: 'ثواني', value: '42' }
              ].map((time, index) => (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-pharaoh-gold text-pharaoh-blue rounded-lg flex items-center justify-center text-2xl font-bold mb-2">
                    {time.value}
                  </div>
                  <div className="text-sm text-white/80">{time.label}</div>
                </div>
              ))}
            </div>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-pharaoh-gold text-pharaoh-blue px-8 py-3 rounded-lg font-bold hover:bg-pharaoh-gold/90 transition-all duration-300"
            >
              تسوق الآن واستفد من العرض
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
