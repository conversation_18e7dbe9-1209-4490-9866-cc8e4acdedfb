'use client'

import { motion } from 'framer-motion'
import { Calendar, Clock, ArrowLeft, ArrowRight, Tag } from 'lucide-react'
import { useLanguage } from '@/hooks/use-language'

export default function NewsSection() {
  const { t, isRTL } = useLanguage()

  const news = [
    {
      id: 1,
      title: 'إطلاق مجموعة جديدة من الهواتف الذكية',
      excerpt: 'نعلن عن وصول أحدث الهواتف الذكية بتقنيات متطورة وأسعار تنافسية لجميع عملائنا الكرام.',
      image: '/news-1.jpg',
      category: 'منتجات جديدة',
      date: '2024-01-20',
      readTime: '3 دقائق',
      author: 'فريق MSB',
      featured: true
    },
    {
      id: 2,
      title: 'توسيع خدمات الصيانة لتشمل محافظات جديدة',
      excerpt: 'نسعد بإعلان توسيع نطاق خدمات الصيانة لتشمل 5 محافظات جديدة مع فريق من الفنيين المتخصصين.',
      image: '/news-2.jpg',
      category: 'خدمات',
      date: '2024-01-18',
      readTime: '2 دقيقة',
      author: 'إدارة الخدمات',
      featured: false
    },
    {
      id: 3,
      title: 'عروض الشتاء الكبرى - خصومات تصل إلى 70%',
      excerpt: 'استمتع بأكبر عروض الشتاء على مجموعة واسعة من المنتجات مع خصومات استثنائية وعروض حصرية.',
      image: '/news-3.jpg',
      category: 'عروض',
      date: '2024-01-15',
      readTime: '4 دقائق',
      author: 'فريق التسويق',
      featured: true
    },
    {
      id: 4,
      title: 'شراكة جديدة مع أكبر الموردين العالميين',
      excerpt: 'نعلن عن شراكة استراتيجية جديدة تضمن لعملائنا الحصول على أفضل المنتجات بأسعار مميزة.',
      image: '/news-4.jpg',
      category: 'أخبار الشركة',
      date: '2024-01-12',
      readTime: '5 دقائق',
      author: 'الإدارة العامة',
      featured: false
    }
  ]

  const categories = [
    { name: 'الكل', count: news.length },
    { name: 'منتجات جديدة', count: 1 },
    { name: 'خدمات', count: 1 },
    { name: 'عروض', count: 1 },
    { name: 'أخبار الشركة', count: 1 }
  ]

  return (
    <section className="py-16 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4">
        {/* العنوان */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-pharaoh text-gray-900 dark:text-white mb-4">
            آخر الأخبار والمقالات
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            تابع آخر الأخبار والتحديثات حول منتجاتنا وخدماتنا الجديدة
          </p>
        </motion.div>

        {/* فلاتر الفئات */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category, index) => (
            <button
              key={index}
              className="px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full hover:bg-pharaoh-gold hover:text-pharaoh-blue transition-all duration-300 border border-gray-200 dark:border-gray-700"
            >
              {category.name} ({category.count})
            </button>
          ))}
        </motion.div>

        {/* المقال المميز */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
          className="mb-12"
        >
          {news.filter(article => article.featured)[0] && (
            <div className="bg-white dark:bg-gray-800 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                {/* الصورة */}
                <div className="relative h-64 lg:h-auto">
                  <div className="w-full h-full bg-gradient-to-br from-pharaoh-blue to-pharaoh-gold flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Tag className="w-8 h-8" />
                      </div>
                      <p className="text-sm">صورة المقال المميز</p>
                    </div>
                  </div>
                  <div className="absolute top-4 right-4 rtl:right-auto rtl:left-4 bg-pharaoh-gold text-pharaoh-blue px-3 py-1 rounded-full text-sm font-bold">
                    مميز
                  </div>
                </div>

                {/* المحتوى */}
                <div className="p-8">
                  <div className="flex items-center space-x-4 rtl:space-x-reverse mb-4">
                    <span className="bg-pharaoh-gold/20 text-pharaoh-blue px-3 py-1 rounded-full text-sm font-medium">
                      {news[0].category}
                    </span>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-500 dark:text-gray-400 text-sm">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(news[0].date).toLocaleDateString('ar-EG')}</span>
                    </div>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-500 dark:text-gray-400 text-sm">
                      <Clock className="w-4 h-4" />
                      <span>{news[0].readTime}</span>
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    {news[0].title}
                  </h3>

                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-6">
                    {news[0].excerpt}
                  </p>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      بواسطة {news[0].author}
                    </span>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-pharaoh-blue text-white px-6 py-2 rounded-lg hover:bg-pharaoh-blue/90 transition-all duration-300"
                    >
                      <span>اقرأ المزيد</span>
                      {isRTL ? <ArrowLeft className="w-4 h-4" /> : <ArrowRight className="w-4 h-4" />}
                    </motion.button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </motion.div>

        {/* شبكة المقالات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {news.filter(article => !article.featured).map((article, index) => (
            <motion.article
              key={article.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              className="bg-white dark:bg-gray-800 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group"
            >
              {/* الصورة */}
              <div className="relative h-48 overflow-hidden">
                <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                  <div className="text-center text-gray-500 dark:text-gray-400">
                    <Tag className="w-8 h-8 mx-auto mb-2" />
                    <p className="text-sm">صورة المقال</p>
                  </div>
                </div>
                <div className="absolute top-4 right-4 rtl:right-auto rtl:left-4 bg-pharaoh-gold/90 text-pharaoh-blue px-3 py-1 rounded-full text-sm font-medium">
                  {article.category}
                </div>
              </div>

              {/* المحتوى */}
              <div className="p-6">
                <div className="flex items-center space-x-4 rtl:space-x-reverse mb-3 text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(article.date).toLocaleDateString('ar-EG')}</span>
                  </div>
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    <Clock className="w-4 h-4" />
                    <span>{article.readTime}</span>
                  </div>
                </div>

                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3 line-clamp-2 group-hover:text-pharaoh-blue dark:group-hover:text-pharaoh-gold transition-colors duration-300">
                  {article.title}
                </h3>

                <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed mb-4 line-clamp-3">
                  {article.excerpt}
                </p>

                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {article.author}
                  </span>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="text-pharaoh-blue dark:text-pharaoh-gold hover:underline text-sm font-medium"
                  >
                    اقرأ المزيد
                  </motion.button>
                </div>
              </div>
            </motion.article>
          ))}
        </div>

        {/* زر عرض المزيد */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="text-center mt-12"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-pharaoh-gold text-pharaoh-blue px-8 py-4 rounded-lg font-bold hover:bg-pharaoh-gold/90 transition-all duration-300"
          >
            <span>عرض جميع المقالات</span>
            {isRTL ? <ArrowLeft className="w-5 h-5" /> : <ArrowRight className="w-5 h-5" />}
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}
