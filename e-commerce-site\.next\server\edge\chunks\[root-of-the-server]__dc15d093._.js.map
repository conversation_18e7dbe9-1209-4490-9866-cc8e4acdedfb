{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import createMiddleware from 'next-intl/middleware';\r\n \r\nexport default createMiddleware({\r\n  // A list of all locales that are supported\r\n  locales: ['en', 'ar'],\r\n \r\n  // Used when no locale matches\r\n  defaultLocale: 'ar'\r\n});\r\n \r\nexport const config = {\r\n  // Match only internationalized pathnames\r\n  matcher: ['/', '/(ar|en)/:path*']\r\n};"], "names": [], "mappings": ";;;;AAAA;;uCAEe,CAAA,GAAA,8LAAA,CAAA,UAAgB,AAAD,EAAE;IAC9B,2CAA2C;IAC3C,SAAS;QAAC;QAAM;KAAK;IAErB,8BAA8B;IAC9B,eAAe;AACjB;AAEO,MAAM,SAAS;IACpB,yCAAyC;IACzC,SAAS;QAAC;QAAK;KAAkB;AACnC"}}]}