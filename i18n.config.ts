/**
 * تكوين نظام الترجمة المتقدم لمركز MSB
 * Advanced Internationalization Configuration for MSB Center
 * 
 * TypeScript Configuration File
 */

export interface LanguageConfig {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  rtl: boolean;
  locale: string;
  currency: string;
  dateFormat: Intl.DateTimeFormatOptions;
  numberFormat: Intl.NumberFormatOptions;
}

export interface TranslationKey {
  [key: string]: string | TranslationKey;
}

export interface I18nConfig {
  defaultLanguage: string;
  fallbackLanguage: string;
  supportedLanguages: LanguageConfig[];
  storageKey: string;
  autoDetect: boolean;
  loadPath: string;
  interpolation: {
    prefix: string;
    suffix: string;
  };
}

// تكوين اللغات المدعومة
export const supportedLanguages: LanguageConfig[] = [
  {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇪🇬',
    rtl: true,
    locale: 'ar-EG',
    currency: 'EGP',
    dateFormat: {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    },
    numberFormat: {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }
  },
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    rtl: false,
    locale: 'en-US',
    currency: 'USD',
    dateFormat: {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    },
    numberFormat: {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }
  },
  {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    rtl: false,
    locale: 'fr-FR',
    currency: 'EUR',
    dateFormat: {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    },
    numberFormat: {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }
  }
];

// التكوين الرئيسي
export const i18nConfig: I18nConfig = {
  defaultLanguage: 'ar',
  fallbackLanguage: 'ar',
  supportedLanguages,
  storageKey: 'msb-language',
  autoDetect: true,
  loadPath: '/locales/{{lng}}.json',
  interpolation: {
    prefix: '{{',
    suffix: '}}'
  }
};

// أنواع البيانات للترجمات
export interface Translations {
  // التنقل
  nav: {
    home: string;
    products: string;
    services: string;
    about: string;
    contact: string;
    cart: string;
    wishlist: string;
    profile: string;
    orders: string;
    login: string;
    register: string;
    logout: string;
  };

  // الموقع العام
  site: {
    title: string;
    tagline: string;
    description: string;
    keywords: string;
    loading: string;
    error: string;
    success: string;
    warning: string;
    info: string;
  };

  // الأزرار والإجراءات
  buttons: {
    add_to_cart: string;
    add_to_wishlist: string;
    buy_now: string;
    view_details: string;
    edit: string;
    delete: string;
    save: string;
    cancel: string;
    submit: string;
    search: string;
    filter: string;
    sort: string;
    clear: string;
    back: string;
    next: string;
    previous: string;
    close: string;
    open: string;
  };

  // المنتجات
  products: {
    title: string;
    subtitle: string;
    featured: string;
    new_arrivals: string;
    best_sellers: string;
    on_sale: string;
    price: string;
    original_price: string;
    discount: string;
    in_stock: string;
    out_of_stock: string;
    quantity: string;
    category: string;
    brand: string;
    rating: string;
    reviews: string;
    specifications: string;
    description: string;
  };

  // السلة والطلبات
  cart: {
    title: string;
    empty: string;
    item_count: string;
    subtotal: string;
    tax: string;
    shipping: string;
    total: string;
    checkout: string;
    continue_shopping: string;
    remove_item: string;
    update_quantity: string;
    apply_coupon: string;
    coupon_code: string;
  };

  // الملف الشخصي
  profile: {
    title: string;
    personal_info: string;
    security: string;
    addresses: string;
    preferences: string;
    notifications: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    birth_date: string;
    gender: string;
    change_password: string;
    two_factor_auth: string;
    active_sessions: string;
  };

  // الطلبات
  orders: {
    title: string;
    order_number: string;
    order_date: string;
    status: string;
    total: string;
    track_order: string;
    order_details: string;
    cancel_order: string;
    reorder: string;
    status_pending: string;
    status_processing: string;
    status_shipped: string;
    status_delivered: string;
    status_cancelled: string;
  };

  // النماذج والتحقق
  forms: {
    required_field: string;
    invalid_email: string;
    invalid_phone: string;
    password_too_short: string;
    passwords_not_match: string;
    invalid_date: string;
    invalid_number: string;
    file_too_large: string;
    invalid_file_type: string;
  };

  // الرسائل
  messages: {
    welcome: string;
    goodbye: string;
    thank_you: string;
    please_wait: string;
    operation_successful: string;
    operation_failed: string;
    no_results_found: string;
    loading_more: string;
    end_of_results: string;
  };

  // التواريخ والأوقات
  datetime: {
    now: string;
    today: string;
    yesterday: string;
    tomorrow: string;
    this_week: string;
    last_week: string;
    this_month: string;
    last_month: string;
    this_year: string;
    last_year: string;
    minutes_ago: string;
    hours_ago: string;
    days_ago: string;
  };

  // الفوتر
  footer: {
    about: string;
    links: string;
    contact: string;
    newsletter: string;
    social_media: string;
    privacy: string;
    terms: string;
    rights: string;
  };
}

// مساعدات TypeScript للترجمة
export type TranslationKeys = keyof Translations;
export type NestedTranslationKeys<T> = T extends object
  ? {
      [K in keyof T]: T[K] extends object
        ? `${K & string}.${NestedTranslationKeys<T[K]> & string}`
        : K & string;
    }[keyof T]
  : never;

export type AllTranslationKeys = NestedTranslationKeys<Translations>;

// وظائف مساعدة للتحقق من صحة التكوين
export function validateLanguageConfig(config: LanguageConfig): boolean {
  return !!(
    config.code &&
    config.name &&
    config.nativeName &&
    config.locale &&
    config.currency
  );
}

export function validateI18nConfig(config: I18nConfig): boolean {
  return !!(
    config.defaultLanguage &&
    config.fallbackLanguage &&
    config.supportedLanguages &&
    config.supportedLanguages.length > 0 &&
    config.storageKey
  );
}

// تصدير التكوين الافتراضي
export default i18nConfig;
