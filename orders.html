<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate="nav.orders">طلباتي - مركز MSB</title>
    <meta name="description" content="طلباتي - تتبع وإدارة جميع طلباتك">
    <meta name="keywords" content="طلباتي, تتبع الطلبات, مركز MSB">

    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

    <!-- ملفات الترجمة والوظائف المشتركة -->
    <script src="js/translations.js"></script>
    <script src="js/common.js"></script>

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'pharaoh-gold': '#D4AF37',
                        'pharaoh-blue': '#003366',
                        'pharaoh-sand': '#F4E4BC',
                        'pharaoh-copper': '#B87333',
                    },
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                        'pharaoh': ['Amiri', 'serif'],
                    }
                }
            }
        }
    </script>

    <style>
        .dark {
            background-color: #1a1a1a;
            color: #ffffff;
        }

        .dark .bg-white {
            background-color: #2d2d2d !important;
        }

        .dark .text-gray-600 {
            color: #a0a0a0 !important;
        }

        .dark .text-gray-700 {
            color: #b0b0b0 !important;
        }

        .dark .border-gray-200 {
            border-color: #404040 !important;
        }

        .order-card {
            transition: all 0.3s ease;
        }

        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .status-badge {
            transition: all 0.3s ease;
        }

        .status-pending {
            background-color: #FEF3C7;
            color: #92400E;
        }

        .status-processing {
            background-color: #DBEAFE;
            color: #1E40AF;
        }

        .status-shipped {
            background-color: #D1FAE5;
            color: #065F46;
        }

        .status-delivered {
            background-color: #D1FAE5;
            color: #065F46;
        }

        .status-cancelled {
            background-color: #FEE2E2;
            color: #991B1B;
        }
    </style>
</head>
<body class="font-arabic bg-gray-50">
    <!-- شريط العروض -->
    <div class="bg-pharaoh-gold text-pharaoh-blue py-2 text-center text-sm font-medium animate-pulse">
        <div class="container mx-auto px-4">
            <span data-translate="orders.track_message">📦 تتبع طلباتك بسهولة واطمئن على وصولها! 📦</span>
        </div>
    </div>

    <!-- الهيدر -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <!-- الهيدر الرئيسي -->
            <div class="flex items-center justify-between h-16">
                <!-- الشعار -->
                <div class="flex items-center space-x-2 space-x-reverse">
                    <div class="w-10 h-10 bg-pharaoh-gold rounded-lg flex items-center justify-center">
                        <span class="text-pharaoh-blue font-bold text-xl">M</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-pharaoh text-pharaoh-blue" data-translate="site.title">مركز MSB</h1>
                        <p class="text-xs text-gray-600" data-translate="site.tagline">One Brand, Every Solution</p>
                    </div>
                </div>

                <!-- شريط البحث -->
                <div class="hidden lg:flex flex-1 max-w-xl mx-8">
                    <div class="relative w-full">
                        <input type="text" data-translate="search.placeholder" placeholder="ابحث عن المنتجات والخدمات..."
                               class="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent">
                        <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            🔍
                        </div>
                    </div>
                </div>

                <!-- أدوات الهيدر -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button id="themeToggle" class="p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="theme.toggle" title="تبديل الثيم">🌙</button>

                    <!-- قائمة اللغات -->
                    <div class="relative">
                        <button id="languageToggle" class="flex items-center p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="language.change" title="تغيير اللغة">
                            <span id="currentFlag">🇪🇬</span>
                            <span class="ml-1 text-sm font-medium" id="currentLang">العربية</span>
                        </button>
                        <div id="languageMenu" class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border hidden z-50">
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors language-option" data-lang="ar" data-flag="🇪🇬" data-name="العربية">
                                <span class="mr-2">🇪🇬</span>
                                <span data-translate="language.arabic">العربية</span>
                                <span class="mr-auto text-green-500" id="check-ar">✓</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors language-option" data-lang="en" data-flag="🇺🇸" data-name="English">
                                <span class="mr-2">🇺🇸</span>
                                <span data-translate="language.english">English</span>
                                <span class="mr-auto text-green-500 hidden" id="check-en">✓</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors language-option" data-lang="fr" data-flag="🇫🇷" data-name="Français">
                                <span class="mr-2">🇫🇷</span>
                                <span data-translate="language.french">Français</span>
                                <span class="mr-auto text-green-500 hidden" id="check-fr">✓</span>
                            </a>
                        </div>
                    </div>

                    <button class="relative p-2 rounded-lg hover:bg-gray-100 transition-colors wishlist-counter" data-translate-title="nav.wishlist" title="المفضلة" onclick="window.location.href='wishlist.html'">
                        ❤️
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-pharaoh-gold text-pharaoh-blue text-xs rounded-full flex items-center justify-center font-bold">3</span>
                    </button>
                    <button class="relative p-2 rounded-lg hover:bg-gray-100 transition-colors cart-counter" data-translate-title="nav.cart" title="سلة التسوق" onclick="window.location.href='cart.html'">
                        🛒
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-pharaoh-gold text-pharaoh-blue text-xs rounded-full flex items-center justify-center font-bold">2</span>
                    </button>

                    <!-- قائمة المستخدم -->
                    <div class="relative">
                        <button id="userToggle" class="p-2 rounded-lg hover:bg-gray-100 transition-colors" data-translate-title="nav.account" title="حسابي">👤</button>
                        <div id="userMenu" class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border hidden z-50">
                            <div class="px-4 py-2 border-b border-gray-200">
                                <p class="text-sm font-medium text-gray-900">أحمد محمد</p>
                                <p class="text-xs text-gray-500"><EMAIL></p>
                            </div>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="profile">
                                <span class="mr-2">👤</span>
                                <span data-translate="nav.account">حسابي</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 bg-pharaoh-gold/20 transition-colors user-option" data-action="orders">
                                <span class="mr-2">📦</span>
                                <span data-translate="nav.orders">طلباتي</span>
                                <span class="mr-auto bg-pharaoh-gold text-pharaoh-blue text-xs px-2 py-1 rounded-full">3</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="wishlist">
                                <span class="mr-2">❤️</span>
                                <span data-translate="nav.wishlist">المفضلة</span>
                                <span class="mr-auto bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">5</span>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors user-option" data-action="settings">
                                <span class="mr-2">⚙️</span>
                                <span data-translate="nav.settings">الإعدادات</span>
                            </a>
                            <hr class="my-1">
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-red-50 transition-colors text-red-600 user-option" data-action="logout">
                                <span class="mr-2">🚪</span>
                                <span data-translate="nav.logout">تسجيل الخروج</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التنقل الرئيسي -->
            <nav class="hidden lg:flex items-center justify-center py-4 border-t border-gray-200">
                <div class="flex items-center space-x-8 space-x-reverse">
                    <a href="index.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.home">الرئيسية</a>
                    <a href="products.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.products">المنتجات</a>
                    <a href="services.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.services">الخدمات</a>
                    <a href="about.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.about">من نحن</a>
                    <a href="contact.html" class="text-gray-700 hover:text-pharaoh-blue transition-colors font-medium" data-translate="nav.contact">اتصل بنا</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- شريط التنقل -->
    <div class="bg-pharaoh-blue text-white py-3">
        <div class="container mx-auto px-4">
            <div class="flex items-center space-x-2 space-x-reverse text-sm">
                <a href="index.html" class="hover:text-pharaoh-gold" data-translate="nav.home">الرئيسية</a>
                <span>←</span>
                <span class="text-pharaoh-gold" data-translate="nav.orders">طلباتي</span>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <main class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
            <div>
                <h1 class="text-3xl font-pharaoh text-pharaoh-blue mb-2" data-translate="orders.title">طلباتي</h1>
                <p class="text-gray-600" data-translate="orders.subtitle">تتبع وإدارة جميع طلباتك من مكان واحد</p>
                <div class="text-sm text-gray-500 mt-2">
                    <span id="ordersCount">5</span> <span data-translate="orders.total_orders">طلبات إجمالية</span>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="flex items-center space-x-6 space-x-reverse mt-4 md:mt-0">
                <div class="text-center">
                    <div class="text-2xl font-bold text-pharaoh-blue">3</div>
                    <div class="text-xs text-gray-600" data-translate="orders.active_orders">طلبات نشطة</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">2</div>
                    <div class="text-xs text-gray-600" data-translate="orders.completed_orders">طلبات مكتملة</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-pharaoh-gold">45,670</div>
                    <div class="text-xs text-gray-600" data-translate="orders.total_spent">إجمالي المشتريات</div>
                </div>
            </div>
        </div>

        <!-- شريط الفلترة والبحث -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex flex-col md:flex-row gap-4">
                <!-- البحث -->
                <div class="flex-1">
                    <div class="relative">
                        <input type="text" id="orderSearch" placeholder="ابحث برقم الطلب أو اسم المنتج..."
                               class="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold focus:border-transparent"
                               onkeyup="searchOrders()">
                        <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            🔍
                        </div>
                    </div>
                </div>

                <!-- فلتر الحالة -->
                <div class="md:w-48">
                    <select id="statusFilter" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold" onchange="filterOrders()">
                        <option value="all" data-translate="orders.all_statuses">جميع الحالات</option>
                        <option value="pending" data-translate="orders.status_pending">في الانتظار</option>
                        <option value="processing" data-translate="orders.status_processing">قيد المعالجة</option>
                        <option value="shipped" data-translate="orders.status_shipped">تم الشحن</option>
                        <option value="delivered" data-translate="orders.status_delivered">تم التسليم</option>
                        <option value="cancelled" data-translate="orders.status_cancelled">ملغي</option>
                    </select>
                </div>

                <!-- فلتر التاريخ -->
                <div class="md:w-48">
                    <select id="dateFilter" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold" onchange="filterOrders()">
                        <option value="all" data-translate="orders.all_dates">جميع التواريخ</option>
                        <option value="today" data-translate="orders.today">اليوم</option>
                        <option value="week" data-translate="orders.this_week">هذا الأسبوع</option>
                        <option value="month" data-translate="orders.this_month">هذا الشهر</option>
                        <option value="year" data-translate="orders.this_year">هذا العام</option>
                    </select>
                </div>

                <!-- فلتر الترتيب -->
                <div class="md:w-48">
                    <select id="sortFilter" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold" onchange="sortOrders()">
                        <option value="newest" data-translate="orders.sort_newest">الأحدث أولاً</option>
                        <option value="oldest" data-translate="orders.sort_oldest">الأقدم أولاً</option>
                        <option value="amount_high" data-translate="orders.sort_amount_high">المبلغ: الأعلى أولاً</option>
                        <option value="amount_low" data-translate="orders.sort_amount_low">المبلغ: الأقل أولاً</option>
                    </select>
                </div>
            </div>

            <!-- أزرار سريعة -->
            <div class="flex flex-wrap gap-2 mt-4 pt-4 border-t border-gray-200">
                <button onclick="quickFilter('active')" class="px-4 py-2 bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors text-sm" data-translate="orders.active_only">
                    الطلبات النشطة فقط
                </button>
                <button onclick="quickFilter('completed')" class="px-4 py-2 bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors text-sm" data-translate="orders.completed_only">
                    الطلبات المكتملة فقط
                </button>
                <button onclick="quickFilter('recent')" class="px-4 py-2 bg-pharaoh-gold/20 text-pharaoh-blue rounded-lg hover:bg-pharaoh-gold/30 transition-colors text-sm" data-translate="orders.recent_orders">
                    الطلبات الأخيرة
                </button>
                <button onclick="clearFilters()" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm" data-translate="orders.clear_filters">
                    مسح الفلاتر
                </button>
            </div>
        </div>

        <!-- قائمة الطلبات -->
        <div id="ordersContainer" class="space-y-6">
            <!-- طلب 1 - قيد المعالجة -->
            <div class="order-card bg-white rounded-lg shadow-lg overflow-hidden" data-order-id="ORD-2024-001" data-status="processing" data-date="2024-01-15" data-amount="15999">
                <div class="p-6">
                    <!-- رأس الطلب -->
                    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                        <div>
                            <h3 class="text-lg font-bold text-pharaoh-blue mb-1">طلب #ORD-2024-001</h3>
                            <p class="text-gray-600 text-sm">تاريخ الطلب: 15 يناير 2024</p>
                        </div>
                        <div class="flex items-center space-x-4 space-x-reverse mt-2 md:mt-0">
                            <span class="status-badge status-processing px-3 py-1 rounded-full text-sm font-medium">قيد المعالجة</span>
                            <span class="text-lg font-bold text-pharaoh-blue">15,999 ج.م</span>
                        </div>
                    </div>

                    <!-- منتجات الطلب -->
                    <div class="border-t border-gray-200 pt-4">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                <span class="text-2xl">📱</span>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">هاتف ذكي متطور</h4>
                                <p class="text-gray-600 text-sm">الكمية: 1 × 15,999 ج.م</p>
                            </div>
                        </div>
                    </div>

                    <!-- تتبع الطلب -->
                    <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <span class="text-blue-600">📦</span>
                                <span class="text-sm font-medium text-blue-800">يتم تحضير طلبك للشحن</span>
                            </div>
                            <span class="text-xs text-blue-600">متوقع الشحن: غداً</span>
                        </div>
                        <div class="mt-2 bg-blue-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: 60%"></div>
                        </div>
                    </div>

                    <!-- أزرار العمليات -->
                    <div class="flex flex-wrap gap-2 mt-4">
                        <button onclick="trackOrder('ORD-2024-001')" class="bg-pharaoh-blue text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors text-sm">
                            تتبع الطلب
                        </button>
                        <button onclick="viewOrderDetails('ORD-2024-001')" class="border border-pharaoh-blue text-pharaoh-blue px-4 py-2 rounded-lg hover:bg-pharaoh-blue hover:text-white transition-colors text-sm">
                            تفاصيل الطلب
                        </button>
                        <button onclick="cancelOrder('ORD-2024-001')" class="border border-red-500 text-red-500 px-4 py-2 rounded-lg hover:bg-red-500 hover:text-white transition-colors text-sm">
                            إلغاء الطلب
                        </button>
                    </div>
                </div>
            </div>

            <!-- طلب 2 - تم الشحن -->
            <div class="order-card bg-white rounded-lg shadow-lg overflow-hidden" data-order-id="ORD-2024-002" data-status="shipped" data-date="2024-01-12" data-amount="45999">
                <div class="p-6">
                    <!-- رأس الطلب -->
                    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                        <div>
                            <h3 class="text-lg font-bold text-pharaoh-blue mb-1">طلب #ORD-2024-002</h3>
                            <p class="text-gray-600 text-sm">تاريخ الطلب: 12 يناير 2024</p>
                        </div>
                        <div class="flex items-center space-x-4 space-x-reverse mt-2 md:mt-0">
                            <span class="status-badge status-shipped px-3 py-1 rounded-full text-sm font-medium">تم الشحن</span>
                            <span class="text-lg font-bold text-pharaoh-blue">45,999 ج.م</span>
                        </div>
                    </div>

                    <!-- منتجات الطلب -->
                    <div class="border-t border-gray-200 pt-4">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                <span class="text-2xl">💻</span>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">لابتوب للألعاب</h4>
                                <p class="text-gray-600 text-sm">الكمية: 1 × 45,999 ج.م</p>
                            </div>
                        </div>
                    </div>

                    <!-- تتبع الطلب -->
                    <div class="mt-4 p-4 bg-green-50 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <span class="text-green-600">🚚</span>
                                <span class="text-sm font-medium text-green-800">طلبك في الطريق إليك</span>
                            </div>
                            <span class="text-xs text-green-600">متوقع الوصول: اليوم</span>
                        </div>
                        <div class="mt-2 bg-green-200 rounded-full h-2">
                            <div class="bg-green-600 h-2 rounded-full" style="width: 85%"></div>
                        </div>
                    </div>

                    <!-- أزرار العمليات -->
                    <div class="flex flex-wrap gap-2 mt-4">
                        <button onclick="trackOrder('ORD-2024-002')" class="bg-pharaoh-blue text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors text-sm">
                            تتبع الطلب
                        </button>
                        <button onclick="viewOrderDetails('ORD-2024-002')" class="border border-pharaoh-blue text-pharaoh-blue px-4 py-2 rounded-lg hover:bg-pharaoh-blue hover:text-white transition-colors text-sm">
                            تفاصيل الطلب
                        </button>
                        <button onclick="contactSupport('ORD-2024-002')" class="border border-pharaoh-gold text-pharaoh-blue px-4 py-2 rounded-lg hover:bg-pharaoh-gold hover:text-pharaoh-blue transition-colors text-sm">
                            تواصل مع الدعم
                        </button>
                    </div>
                </div>
            </div>

            <!-- طلب 3 - تم التسليم -->
            <div class="order-card bg-white rounded-lg shadow-lg overflow-hidden" data-order-id="ORD-2024-003" data-status="delivered" data-date="2024-01-08" data-amount="1999">
                <div class="p-6">
                    <!-- رأس الطلب -->
                    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                        <div>
                            <h3 class="text-lg font-bold text-pharaoh-blue mb-1">طلب #ORD-2024-003</h3>
                            <p class="text-gray-600 text-sm">تاريخ الطلب: 8 يناير 2024</p>
                        </div>
                        <div class="flex items-center space-x-4 space-x-reverse mt-2 md:mt-0">
                            <span class="status-badge status-delivered px-3 py-1 rounded-full text-sm font-medium">تم التسليم</span>
                            <span class="text-lg font-bold text-pharaoh-blue">1,999 ج.م</span>
                        </div>
                    </div>

                    <!-- منتجات الطلب -->
                    <div class="border-t border-gray-200 pt-4">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                <span class="text-2xl">🎧</span>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">سماعات لاسلكية</h4>
                                <p class="text-gray-600 text-sm">الكمية: 1 × 1,999 ج.م</p>
                            </div>
                        </div>
                    </div>

                    <!-- حالة التسليم -->
                    <div class="mt-4 p-4 bg-green-50 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <span class="text-green-600">✅</span>
                                <span class="text-sm font-medium text-green-800">تم تسليم الطلب بنجاح</span>
                            </div>
                            <span class="text-xs text-green-600">تم التسليم: 10 يناير 2024</span>
                        </div>
                        <div class="mt-2 bg-green-200 rounded-full h-2">
                            <div class="bg-green-600 h-2 rounded-full" style="width: 100%"></div>
                        </div>
                    </div>

                    <!-- أزرار العمليات -->
                    <div class="flex flex-wrap gap-2 mt-4">
                        <button onclick="rateOrder('ORD-2024-003')" class="bg-pharaoh-gold text-pharaoh-blue px-4 py-2 rounded-lg hover:bg-yellow-300 transition-colors text-sm">
                            تقييم المنتج
                        </button>
                        <button onclick="reorderItems('ORD-2024-003')" class="border border-pharaoh-blue text-pharaoh-blue px-4 py-2 rounded-lg hover:bg-pharaoh-blue hover:text-white transition-colors text-sm">
                            إعادة الطلب
                        </button>
                        <button onclick="viewOrderDetails('ORD-2024-003')" class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors text-sm">
                            تفاصيل الطلب
                        </button>
                    </div>
                </div>
            </div>

            <!-- طلب 4 - في الانتظار -->
            <div class="order-card bg-white rounded-lg shadow-lg overflow-hidden" data-order-id="ORD-2024-004" data-status="pending" data-date="2024-01-16" data-amount="8999">
                <div class="p-6">
                    <!-- رأس الطلب -->
                    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                        <div>
                            <h3 class="text-lg font-bold text-pharaoh-blue mb-1">طلب #ORD-2024-004</h3>
                            <p class="text-gray-600 text-sm">تاريخ الطلب: 16 يناير 2024</p>
                        </div>
                        <div class="flex items-center space-x-4 space-x-reverse mt-2 md:mt-0">
                            <span class="status-badge status-pending px-3 py-1 rounded-full text-sm font-medium">في الانتظار</span>
                            <span class="text-lg font-bold text-pharaoh-blue">8,999 ج.م</span>
                        </div>
                    </div>

                    <!-- منتجات الطلب -->
                    <div class="border-t border-gray-200 pt-4">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                <span class="text-2xl">⌚</span>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">ساعة ذكية رياضية</h4>
                                <p class="text-gray-600 text-sm">الكمية: 1 × 8,999 ج.م</p>
                            </div>
                        </div>
                    </div>

                    <!-- حالة الانتظار -->
                    <div class="mt-4 p-4 bg-yellow-50 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <span class="text-yellow-600">⏳</span>
                                <span class="text-sm font-medium text-yellow-800">في انتظار تأكيد الدفع</span>
                            </div>
                            <span class="text-xs text-yellow-600">يتطلب إجراء</span>
                        </div>
                        <div class="mt-2 bg-yellow-200 rounded-full h-2">
                            <div class="bg-yellow-600 h-2 rounded-full" style="width: 25%"></div>
                        </div>
                    </div>

                    <!-- أزرار العمليات -->
                    <div class="flex flex-wrap gap-2 mt-4">
                        <button onclick="completePayment('ORD-2024-004')" class="bg-pharaoh-gold text-pharaoh-blue px-4 py-2 rounded-lg hover:bg-yellow-300 transition-colors text-sm">
                            إكمال الدفع
                        </button>
                        <button onclick="viewOrderDetails('ORD-2024-004')" class="border border-pharaoh-blue text-pharaoh-blue px-4 py-2 rounded-lg hover:bg-pharaoh-blue hover:text-white transition-colors text-sm">
                            تفاصيل الطلب
                        </button>
                        <button onclick="cancelOrder('ORD-2024-004')" class="border border-red-500 text-red-500 px-4 py-2 rounded-lg hover:bg-red-500 hover:text-white transition-colors text-sm">
                            إلغاء الطلب
                        </button>
                    </div>
                </div>
            </div>

            <!-- طلب 5 - ملغي -->
            <div class="order-card bg-white rounded-lg shadow-lg overflow-hidden" data-order-id="ORD-2024-005" data-status="cancelled" data-date="2024-01-05" data-amount="3499">
                <div class="p-6">
                    <!-- رأس الطلب -->
                    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                        <div>
                            <h3 class="text-lg font-bold text-pharaoh-blue mb-1">طلب #ORD-2024-005</h3>
                            <p class="text-gray-600 text-sm">تاريخ الطلب: 5 يناير 2024</p>
                        </div>
                        <div class="flex items-center space-x-4 space-x-reverse mt-2 md:mt-0">
                            <span class="status-badge status-cancelled px-3 py-1 rounded-full text-sm font-medium">ملغي</span>
                            <span class="text-lg font-bold text-gray-500">3,499 ج.م</span>
                        </div>
                    </div>

                    <!-- منتجات الطلب -->
                    <div class="border-t border-gray-200 pt-4">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                <span class="text-2xl">📷</span>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">كاميرا رقمية</h4>
                                <p class="text-gray-600 text-sm">الكمية: 1 × 3,499 ج.م</p>
                            </div>
                        </div>
                    </div>

                    <!-- حالة الإلغاء -->
                    <div class="mt-4 p-4 bg-red-50 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <span class="text-red-600">❌</span>
                                <span class="text-sm font-medium text-red-800">تم إلغاء الطلب</span>
                            </div>
                            <span class="text-xs text-red-600">تم الإلغاء: 6 يناير 2024</span>
                        </div>
                        <p class="text-xs text-red-600 mt-2">السبب: نفاد المخزون</p>
                    </div>

                    <!-- أزرار العمليات -->
                    <div class="flex flex-wrap gap-2 mt-4">
                        <button onclick="reorderItems('ORD-2024-005')" class="bg-pharaoh-blue text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors text-sm">
                            إعادة الطلب
                        </button>
                        <button onclick="viewOrderDetails('ORD-2024-005')" class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors text-sm">
                            تفاصيل الطلب
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- رسالة عدم وجود طلبات (مخفية افتراضياً) -->
        <div id="noOrdersMessage" class="hidden text-center py-16">
            <div class="max-w-md mx-auto">
                <div class="text-6xl mb-4">📦</div>
                <h2 class="text-2xl font-bold text-gray-800 mb-4" data-translate="orders.no_orders_title">لا توجد طلبات</h2>
                <p class="text-gray-600 mb-6" data-translate="orders.no_orders_message">لم تقم بأي طلبات بعد. ابدأ في استكشاف منتجاتنا!</p>
                <button onclick="window.location.href='products.html'" class="bg-pharaoh-gold text-pharaoh-blue px-8 py-3 rounded-lg font-bold hover:bg-yellow-300 transition-colors" data-translate="orders.browse_products">
                    تصفح المنتجات
                </button>
            </div>
        </div>
    </main>

    <script>
        // بيانات الطلبات
        const ordersData = [
            { id: 'ORD-2024-001', status: 'processing', date: '2024-01-15', amount: 15999, product: 'هاتف ذكي متطور' },
            { id: 'ORD-2024-002', status: 'shipped', date: '2024-01-12', amount: 45999, product: 'لابتوب للألعاب' },
            { id: 'ORD-2024-003', status: 'delivered', date: '2024-01-08', amount: 1999, product: 'سماعات لاسلكية' },
            { id: 'ORD-2024-004', status: 'pending', date: '2024-01-16', amount: 8999, product: 'ساعة ذكية رياضية' },
            { id: 'ORD-2024-005', status: 'cancelled', date: '2024-01-05', amount: 3499, product: 'كاميرا رقمية' }
        ];

        // البحث في الطلبات
        function searchOrders() {
            const searchTerm = document.getElementById('orderSearch').value.toLowerCase();
            const orderCards = document.querySelectorAll('.order-card');

            orderCards.forEach(card => {
                const orderId = card.dataset.orderId.toLowerCase();
                const productName = card.querySelector('h4').textContent.toLowerCase();

                if (orderId.includes(searchTerm) || productName.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });

            updateOrdersCount();
        }

        // فلترة الطلبات
        function filterOrders() {
            const statusFilter = document.getElementById('statusFilter').value;
            const dateFilter = document.getElementById('dateFilter').value;
            const orderCards = document.querySelectorAll('.order-card');

            orderCards.forEach(card => {
                let showCard = true;

                // فلتر الحالة
                if (statusFilter !== 'all' && card.dataset.status !== statusFilter) {
                    showCard = false;
                }

                // فلتر التاريخ
                if (dateFilter !== 'all') {
                    const orderDate = new Date(card.dataset.date);
                    const today = new Date();

                    switch(dateFilter) {
                        case 'today':
                            if (orderDate.toDateString() !== today.toDateString()) {
                                showCard = false;
                            }
                            break;
                        case 'week':
                            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                            if (orderDate < weekAgo) {
                                showCard = false;
                            }
                            break;
                        case 'month':
                            if (orderDate.getMonth() !== today.getMonth() || orderDate.getFullYear() !== today.getFullYear()) {
                                showCard = false;
                            }
                            break;
                        case 'year':
                            if (orderDate.getFullYear() !== today.getFullYear()) {
                                showCard = false;
                            }
                            break;
                    }
                }

                card.style.display = showCard ? 'block' : 'none';
            });

            updateOrdersCount();
        }

        // ترتيب الطلبات
        function sortOrders() {
            const sortValue = document.getElementById('sortFilter').value;
            const container = document.getElementById('ordersContainer');
            const orderCards = Array.from(container.children).filter(child => child.classList.contains('order-card'));

            orderCards.sort((a, b) => {
                switch(sortValue) {
                    case 'newest':
                        return new Date(b.dataset.date) - new Date(a.dataset.date);
                    case 'oldest':
                        return new Date(a.dataset.date) - new Date(b.dataset.date);
                    case 'amount_high':
                        return parseInt(b.dataset.amount) - parseInt(a.dataset.amount);
                    case 'amount_low':
                        return parseInt(a.dataset.amount) - parseInt(b.dataset.amount);
                    default:
                        return 0;
                }
            });

            // إعادة ترتيب العناصر في DOM
            orderCards.forEach(card => container.appendChild(card));
            showNotification(t('orders.sorted', 'تم ترتيب الطلبات'), 'info');
        }

        // فلترة سريعة
        function quickFilter(type) {
            const statusFilter = document.getElementById('statusFilter');
            const dateFilter = document.getElementById('dateFilter');

            switch(type) {
                case 'active':
                    statusFilter.value = 'processing';
                    break;
                case 'completed':
                    statusFilter.value = 'delivered';
                    break;
                case 'recent':
                    dateFilter.value = 'week';
                    break;
            }

            filterOrders();
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('orderSearch').value = '';
            document.getElementById('statusFilter').value = 'all';
            document.getElementById('dateFilter').value = 'all';
            document.getElementById('sortFilter').value = 'newest';

            const orderCards = document.querySelectorAll('.order-card');
            orderCards.forEach(card => {
                card.style.display = 'block';
            });

            updateOrdersCount();
            showNotification(t('orders.filters_cleared', 'تم مسح جميع الفلاتر'), 'info');
        }

        // تتبع الطلب
        function trackOrder(orderId) {
            showNotification(t('orders.tracking_order', 'فتح صفحة تتبع الطلب: ') + orderId, 'info');
            // هنا يمكن إضافة منطق تتبع الطلب
        }

        // عرض تفاصيل الطلب
        function viewOrderDetails(orderId) {
            showNotification(t('orders.viewing_details', 'عرض تفاصيل الطلب: ') + orderId, 'info');
            // هنا يمكن إضافة منطق عرض التفاصيل
        }

        // إلغاء الطلب
        function cancelOrder(orderId) {
            if (confirm(t('orders.confirm_cancel', 'هل أنت متأكد من إلغاء هذا الطلب؟'))) {
                showNotification(t('orders.order_cancelled', 'تم إلغاء الطلب: ') + orderId, 'success');
                // هنا يمكن إضافة منطق إلغاء الطلب
            }
        }

        // إكمال الدفع
        function completePayment(orderId) {
            showNotification(t('orders.redirecting_payment', 'جاري التوجه لصفحة الدفع...'), 'info');
            setTimeout(() => {
                showNotification(t('orders.payment_completed', 'تم إكمال الدفع بنجاح'), 'success');
            }, 2000);
        }

        // تقييم الطلب
        function rateOrder(orderId) {
            showNotification(t('orders.rating_product', 'فتح نموذج تقييم المنتج'), 'info');
            // هنا يمكن إضافة منطق التقييم
        }

        // إعادة الطلب
        function reorderItems(orderId) {
            if (confirm(t('orders.confirm_reorder', 'هل تريد إعادة طلب نفس المنتجات؟'))) {
                showNotification(t('orders.items_added_cart', 'تم إضافة المنتجات للسلة'), 'success');
                // هنا يمكن إضافة منطق إعادة الطلب
            }
        }

        // التواصل مع الدعم
        function contactSupport(orderId) {
            showNotification(t('orders.contacting_support', 'فتح محادثة مع الدعم الفني'), 'info');
            // هنا يمكن إضافة منطق التواصل مع الدعم
        }

        // تحديث عداد الطلبات
        function updateOrdersCount() {
            const visibleOrders = document.querySelectorAll('.order-card[style*="block"], .order-card:not([style*="none"])').length;
            document.getElementById('ordersCount').textContent = visibleOrders;

            // إظهار/إخفاء رسالة عدم وجود طلبات
            const noOrdersMessage = document.getElementById('noOrdersMessage');
            const ordersContainer = document.getElementById('ordersContainer');

            if (visibleOrders === 0) {
                ordersContainer.style.display = 'none';
                noOrdersMessage.classList.remove('hidden');
            } else {
                ordersContainer.style.display = 'block';
                noOrdersMessage.classList.add('hidden');
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateOrdersCount();

            // إضافة تأثيرات hover للطلبات
            document.querySelectorAll('.order-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
