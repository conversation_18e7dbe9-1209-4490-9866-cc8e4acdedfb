"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _useerrorhandler = require("../errors/use-error-handler");
(0, _useerrorhandler.handleGlobalErrors)();

if ((typeof exports.default === 'function' || (typeof exports.default === 'object' && exports.default !== null)) && typeof exports.default.__esModule === 'undefined') {
  Object.defineProperty(exports.default, '__esModule', { value: true });
  Object.assign(exports.default, exports);
  module.exports = exports.default;
}

//# sourceMappingURL=handle-global-errors.js.map