#!/usr/bin/env python3
"""
خادم محلي بسيط لتشغيل موقع مركز MSB
Simple Local Server for MSB Center Website
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

# إعدادات الخادم
PORT = 8000
HOST = 'localhost'

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # إضافة headers لدعم CORS والملفات العربية
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.send_header('Cache-Control', 'no-cache')
        super().end_headers()

def main():
    # التأكد من وجود الملفات
    if not os.path.exists('index.html'):
        print("❌ خطأ: ملف index.html غير موجود في المجلد الحالي")
        print("تأكد من تشغيل هذا الملف في نفس مجلد الموقع")
        input("اضغط Enter للخروج...")
        return

    # إنشاء الخادم
    try:
        with socketserver.TCPServer((HOST, PORT), CustomHTTPRequestHandler) as httpd:
            print(f"🚀 تم تشغيل خادم مركز MSB على:")
            print(f"   http://{HOST}:{PORT}")
            print(f"   http://127.0.0.1:{PORT}")
            print()
            print("📋 الملفات المتاحة:")
            
            # عرض قائمة بالملفات المتاحة
            html_files = [f for f in os.listdir('.') if f.endswith('.html')]
            for i, file in enumerate(html_files, 1):
                print(f"   {i}. {file}")
            
            print()
            print("🌐 سيتم فتح الموقع في المتصفح تلقائياً...")
            print("⏹️  للإيقاف: اضغط Ctrl+C")
            print("=" * 50)
            
            # فتح المتصفح تلقائياً
            webbrowser.open(f'http://{HOST}:{PORT}')
            
            # تشغيل الخادم
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 10048:  # Port already in use
            print(f"❌ المنفذ {PORT} مستخدم بالفعل")
            print("جرب منفذ آخر أو أغلق التطبيق الذي يستخدم هذا المنفذ")
        else:
            print(f"❌ خطأ في تشغيل الخادم: {e}")
        input("اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
        print("شكراً لاستخدام موقع مركز MSB!")

if __name__ == "__main__":
    main()
