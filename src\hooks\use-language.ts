'use client'

import { useContext } from 'react'

// نوع مؤقت للسياق
interface LanguageContextType {
  language: 'ar' | 'en' | 'fr'
  setLanguage: (lang: 'ar' | 'en' | 'fr') => void
  t: (key: string) => string
  isRTL: boolean
}

// سياق مؤقت
const defaultContext: LanguageContextType = {
  language: 'ar',
  setLanguage: () => {},
  t: (key: string) => key,
  isRTL: true
}

export function useLanguage(): LanguageContextType {
  // إرجاع السياق الافتراضي مؤقتاً
  return defaultContext
}
