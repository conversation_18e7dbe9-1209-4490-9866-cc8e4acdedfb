/**
 * الوظائف المشتركة الشاملة لموقع مركز MSB
 * MSB Center Comprehensive Common Functions
 *
 * يشمل جميع الوظائف المطلوبة:
 * - نظام الترجمة الفوري
 * - تبديل الثيم في جميع الصفحات
 * - تفعيل جميع الأزرار
 * - ربط الصفحات
 * - المساعد الذكي
 */

// وظيفة تبديل الثيم
function toggleTheme() {
    const body = document.body;
    const themeToggle = document.getElementById('themeToggle');

    if (body.classList.contains('dark')) {
        body.classList.remove('dark');
        if (themeToggle) themeToggle.textContent = '🌙';
        localStorage.setItem('theme', 'light');
        showNotification(t('msg.theme_changed') + ' - ' + t('theme.light'), 'success');
    } else {
        body.classList.add('dark');
        if (themeToggle) themeToggle.textContent = '☀️';
        localStorage.setItem('theme', 'dark');
        showNotification(t('msg.theme_changed') + ' - ' + t('theme.dark'), 'success');
    }
}

// تحميل الثيم المحفوظ
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme');
    const themeToggle = document.getElementById('themeToggle');

    if (savedTheme === 'dark') {
        document.body.classList.add('dark');
        if (themeToggle) themeToggle.textContent = '☀️';
    } else {
        if (themeToggle) themeToggle.textContent = '🌙';
    }

    // ربط زر تبديل الثيم
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }
}

// وظيفة الإشعارات المحسنة
function showNotification(message, type = 'info', duration = 3000) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full max-w-sm`;

    // تحديد الألوان والأيقونات حسب النوع
    const typeConfig = {
        success: { bg: 'bg-green-500', icon: '✅' },
        warning: { bg: 'bg-yellow-500', icon: '⚠️' },
        error: { bg: 'bg-red-500', icon: '❌' },
        info: { bg: 'bg-blue-500', icon: 'ℹ️' }
    };

    const config = typeConfig[type] || typeConfig.info;
    notification.classList.add(config.bg, 'text-white');

    notification.innerHTML = `
        <div class="flex items-center">
            <span class="mr-2 text-lg">${config.icon}</span>
            <span class="flex-1">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-200 text-xl">✕</button>
        </div>
    `;

    document.body.appendChild(notification);

    // إظهار الإشعار
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // إخفاء الإشعار تلقائياً
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, duration);

    return notification;
}

// وظيفة إدارة القوائم المنسدلة
function initializeDropdowns() {
    // قائمة اللغات
    const languageToggle = document.getElementById('languageToggle');
    const languageMenu = document.getElementById('languageMenu');

    if (languageToggle && languageMenu) {
        languageToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            languageMenu.classList.toggle('hidden');
            // إغلاق القوائم الأخرى
            const userMenu = document.getElementById('userMenu');
            if (userMenu) userMenu.classList.add('hidden');
        });

        // تبديل اللغات
        document.querySelectorAll('.language-option').forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const lang = this.dataset.lang;
                changeLanguage(lang);
                languageMenu.classList.add('hidden');
            });
        });
    }

    // قائمة المستخدم
    const userToggle = document.getElementById('userToggle');
    const userMenu = document.getElementById('userMenu');

    if (userToggle && userMenu) {
        userToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenu.classList.toggle('hidden');
            // إغلاق القوائم الأخرى
            if (languageMenu) languageMenu.classList.add('hidden');
        });

        // خيارات قائمة المستخدم
        document.querySelectorAll('.user-option').forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const action = this.dataset.action;
                handleUserAction(action);
                userMenu.classList.add('hidden');
            });
        });
    }

    // إغلاق القوائم عند النقر خارجها
    document.addEventListener('click', function() {
        if (languageMenu) languageMenu.classList.add('hidden');
        if (userMenu) userMenu.classList.add('hidden');
    });
}

// وظيفة التعامل مع إجراءات المستخدم
function handleUserAction(action) {
    switch(action) {
        case 'profile':
            showNotification(t('nav.account') + '...', 'info');
            setTimeout(() => {
                window.location.href = 'profile.html';
            }, 500);
            break;
        case 'orders':
            showNotification(t('nav.orders') + '...', 'info');
            setTimeout(() => {
                window.location.href = 'orders.html';
            }, 500);
            break;
        case 'wishlist':
            showNotification(t('nav.wishlist') + '...', 'info');
            setTimeout(() => {
                window.location.href = 'wishlist.html';
            }, 500);
            break;
        case 'settings':
            showNotification(t('nav.settings') + '...', 'info');
            setTimeout(() => {
                window.location.href = 'settings.html';
            }, 500);
            break;
        case 'logout':
            if (confirm(t('msg.confirm_delete'))) {
                showNotification(t('nav.logout') + '...', 'warning');
                setTimeout(() => {
                    showNotification(t('msg.success'), 'success');
                    // تنفيذ تسجيل الخروج
                }, 1000);
            }
            break;
    }
}

// وظيفة إضافة منتج للسلة
function addToCart(productId, productName) {
    // محاكاة إضافة المنتج للسلة
    showNotification(t('msg.item_added_cart') + ': ' + productName, 'success');

    // تحديث عداد السلة
    updateCartCounter();
}

// وظيفة إضافة منتج للمفضلة
function addToWishlist(productId, productName) {
    // محاكاة إضافة المنتج للمفضلة
    showNotification(t('msg.item_added_wishlist') + ': ' + productName, 'success');

    // تحديث عداد المفضلة
    updateWishlistCounter();
}

// تحديث عداد السلة
function updateCartCounter() {
    const cartCounter = document.querySelector('.cart-counter');
    if (cartCounter) {
        let count = parseInt(cartCounter.textContent) || 0;
        cartCounter.textContent = count + 1;
    }
}

// تحديث عداد المفضلة
function updateWishlistCounter() {
    const wishlistCounter = document.querySelector('.wishlist-counter');
    if (wishlistCounter) {
        let count = parseInt(wishlistCounter.textContent) || 0;
        wishlistCounter.textContent = count + 1;
    }
}

// وظيفة البحث
function initializeSearch() {
    const searchInput = document.querySelector('input[type="text"][placeholder*="بحث"], input[type="text"][placeholder*="Search"]');

    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch(this.value);
            }
        });

        // تحديث placeholder حسب اللغة
        searchInput.setAttribute('data-translate', 'search.placeholder');
    }
}

// تنفيذ البحث
function performSearch(query) {
    if (query.trim()) {
        showNotification(t('search.results') + ': ' + query, 'info');
        // هنا يمكن إضافة منطق البحث الفعلي
    }
}

// وظيفة تأثيرات التمرير
function initializeScrollEffects() {
    // تأثير التمرير السلس للروابط
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // تأثير الظهور عند التمرير
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // مراقبة العناصر للتأثيرات
    document.querySelectorAll('section, .card, .product-item').forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(element);
    });
}

// وظيفة تهيئة PWA
function initializePWA() {
    // تسجيل Service Worker
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/service-worker.js')
            .then(registration => {
                console.log('Service Worker registered successfully');
            })
            .catch(error => {
                console.log('Service Worker registration failed');
            });
    }

    // زر تثبيت التطبيق
    let deferredPrompt;
    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;

        const installButton = document.createElement('button');
        installButton.textContent = '📱 ' + t('btn.install_app', 'تثبيت التطبيق');
        installButton.className = 'fixed bottom-4 left-4 bg-pharaoh-gold text-pharaoh-blue px-4 py-2 rounded-lg shadow-lg z-50';

        installButton.addEventListener('click', () => {
            deferredPrompt.prompt();
            deferredPrompt.userChoice.then((choiceResult) => {
                if (choiceResult.outcome === 'accepted') {
                    showNotification(t('msg.app_installed', 'تم تثبيت التطبيق'), 'success');
                }
                deferredPrompt = null;
                installButton.remove();
            });
        });

        document.body.appendChild(installButton);
    });
}

// وظيفة التهيئة الرئيسية
function initializeApp() {
    // تهيئة اللغة والثيم
    initializeLanguage();
    initializeTheme();

    // تهيئة المكونات التفاعلية
    initializeDropdowns();
    initializeSearch();
    initializeScrollEffects();
    initializePWA();

    // تحديث الترجمات
    updateTranslations();

    console.log('🎉 تم تهيئة موقع مركز MSB بنجاح');
}

// تشغيل التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeApp);

// وظيفة حجز الخدمات
function bookService(serviceType) {
    const serviceNames = {
        maintenance: t('services.maintenance'),
        delivery: t('services.delivery'),
        installation: t('services.installation'),
        support: t('services.support')
    };

    const serviceName = serviceNames[serviceType] || serviceType;

    // إظهار نموذج حجز الخدمة
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-pharaoh-blue">${t('services.book_now')}: ${serviceName}</h3>
                <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700 text-2xl">×</button>
            </div>
            <form onsubmit="submitServiceBooking(event, '${serviceType}')">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-1">${t('form.name')}</label>
                        <input type="text" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">${t('form.phone')}</label>
                        <input type="tel" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">${t('form.address')}</label>
                        <textarea required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold" rows="3"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">${t('form.message')}</label>
                        <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pharaoh-gold" rows="3" placeholder="${t('services.describe_issue', 'اوصف المشكلة أو الخدمة المطلوبة...')}"></textarea>
                    </div>
                </div>
                <div class="flex gap-3 mt-6">
                    <button type="submit" class="flex-1 bg-pharaoh-blue text-white py-2 rounded-lg hover:bg-blue-800 transition-colors">
                        ${t('services.book_now')}
                    </button>
                    <button type="button" onclick="this.closest('.fixed').remove()" class="flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                        ${t('form.cancel')}
                    </button>
                </div>
            </form>
        </div>
    `;

    document.body.appendChild(modal);

    // التركيز على أول حقل
    setTimeout(() => {
        modal.querySelector('input').focus();
    }, 100);
}

// وظيفة إرسال طلب حجز الخدمة
function submitServiceBooking(event, serviceType) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);

    // محاكاة إرسال الطلب
    showNotification(t('msg.saving'), 'info');

    setTimeout(() => {
        showNotification(t('services.booking_success', 'تم حجز الخدمة بنجاح! سنتواصل معك قريباً'), 'success');
        form.closest('.fixed').remove();
    }, 1500);
}

// ===============================
// نظام الترجمة الفوري الشامل
// ===============================

let currentLanguage = localStorage.getItem('msb-language') || 'ar';

// وظيفة الترجمة المحسنة
function t(key, fallback = key) {
    const lang = currentLanguage;
    return translations[lang] && translations[lang][key] ? translations[lang][key] : fallback;
}

// تطبيق الترجمة الفورية على جميع العناصر
function applyTranslations() {
    // ترجمة العناصر مع data-translate
    document.querySelectorAll('[data-translate]').forEach(element => {
        const key = element.getAttribute('data-translate');
        const translation = t(key);
        if (translation !== key) {
            element.textContent = translation;
        }
    });

    // ترجمة العناصر مع data-translate-placeholder
    document.querySelectorAll('[data-translate-placeholder]').forEach(element => {
        const key = element.getAttribute('data-translate-placeholder');
        const translation = t(key);
        if (translation !== key) {
            element.placeholder = translation;
        }
    });

    // ترجمة العناصر مع data-translate-title
    document.querySelectorAll('[data-translate-title]').forEach(element => {
        const key = element.getAttribute('data-translate-title');
        const translation = t(key);
        if (translation !== key) {
            element.title = translation;
        }
    });

    // ترجمة العناصر مع data-translate-alt
    document.querySelectorAll('[data-translate-alt]').forEach(element => {
        const key = element.getAttribute('data-translate-alt');
        const translation = t(key);
        if (translation !== key) {
            element.alt = translation;
        }
    });

    // تحديث اتجاه الصفحة
    const html = document.documentElement;
    if (currentLanguage === 'ar') {
        html.dir = 'rtl';
        html.lang = 'ar';
    } else {
        html.dir = 'ltr';
        html.lang = currentLanguage;
    }
}

// تغيير اللغة مع الترجمة الفورية
function changeLanguage(newLanguage) {
    if (newLanguage === currentLanguage) return;

    currentLanguage = newLanguage;
    localStorage.setItem('msb-language', newLanguage);

    // تطبيق الترجمة فوراً
    applyTranslations();

    // تحديث واجهة تبديل اللغة
    updateLanguageUI();

    // إشعار بتغيير اللغة
    const languageNames = {
        'ar': 'العربية',
        'en': 'English',
        'fr': 'Français'
    };
    showNotification(`تم تغيير اللغة إلى ${languageNames[newLanguage]}`, 'success');
}

// تحديث واجهة تبديل اللغة
function updateLanguageUI() {
    const currentFlag = document.getElementById('currentFlag');
    const currentLang = document.getElementById('currentLang');

    const languageData = {
        'ar': { flag: '🇪🇬', name: 'العربية' },
        'en': { flag: '🇺🇸', name: 'English' },
        'fr': { flag: '🇫🇷', name: 'Français' }
    };

    if (currentFlag && currentLang) {
        currentFlag.textContent = languageData[currentLanguage].flag;
        currentLang.textContent = languageData[currentLanguage].name;
    }

    // تحديث علامات التحديد
    document.querySelectorAll('[id^="check-"]').forEach(check => {
        check.classList.add('hidden');
    });

    const currentCheck = document.getElementById(`check-${currentLanguage}`);
    if (currentCheck) {
        currentCheck.classList.remove('hidden');
    }
}

// تهيئة نظام الترجمة
function initializeTranslation() {
    // تطبيق الترجمة الأولية
    applyTranslations();
    updateLanguageUI();

    // ربط أحداث تغيير اللغة
    document.querySelectorAll('.language-option').forEach(option => {
        option.addEventListener('click', (e) => {
            e.preventDefault();
            const lang = option.getAttribute('data-lang');
            changeLanguage(lang);

            // إغلاق القائمة
            const languageMenu = document.getElementById('languageMenu');
            if (languageMenu) {
                languageMenu.classList.add('hidden');
            }
        });
    });
}

// ===============================
// نظام تفعيل جميع الأزرار
// ===============================

// تفعيل جميع أزرار التنقل
function initializeNavigation() {
    // أزرار التنقل الرئيسية
    const navLinks = {
        'nav-home': 'index.html',
        'nav-products': 'products.html',
        'nav-services': 'services.html',
        'nav-about': 'about.html',
        'nav-contact': 'contact.html',
        'nav-cart': 'cart.html',
        'nav-wishlist': 'wishlist.html',
        'nav-profile': 'profile.html',
        'nav-orders': 'orders.html'
    };

    // ربط الروابط
    Object.entries(navLinks).forEach(([id, url]) => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('click', (e) => {
                e.preventDefault();
                window.location.href = url;
            });
        }
    });

    // أزرار التنقل بالكلاس
    document.querySelectorAll('[data-nav]').forEach(element => {
        element.addEventListener('click', (e) => {
            e.preventDefault();
            const target = element.getAttribute('data-nav');
            if (target) {
                window.location.href = target;
            }
        });
    });
}

// تفعيل أزرار الإجراءات
function initializeActionButtons() {
    // أزرار إضافة للسلة
    document.querySelectorAll('.add-to-cart-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();
            const productId = button.getAttribute('data-product-id') || 'unknown';
            addToCart(productId, 1);
        });
    });

    // أزرار إضافة للمفضلة
    document.querySelectorAll('.add-to-wishlist-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();
            const productId = button.getAttribute('data-product-id') || 'unknown';
            addToWishlist(productId);
        });
    });

    // أزرار الشراء السريع
    document.querySelectorAll('.buy-now-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();
            const productId = button.getAttribute('data-product-id') || 'unknown';
            buyNow(productId);
        });
    });

    // أزرار عرض التفاصيل
    document.querySelectorAll('.view-details-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();
            const productId = button.getAttribute('data-product-id') || 'unknown';
            viewProductDetails(productId);
        });
    });
}

// تفعيل أزرار الهيدر
function initializeHeaderButtons() {
    // زر البحث
    const searchBtn = document.getElementById('searchBtn');
    const searchInput = document.getElementById('searchInput');

    if (searchBtn && searchInput) {
        searchBtn.addEventListener('click', performSearch);
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }

    // زر السلة
    const cartBtn = document.getElementById('cartBtn');
    if (cartBtn) {
        cartBtn.addEventListener('click', (e) => {
            e.preventDefault();
            window.location.href = 'cart.html';
        });
    }

    // زر المفضلة
    const wishlistBtn = document.getElementById('wishlistBtn');
    if (wishlistBtn) {
        wishlistBtn.addEventListener('click', (e) => {
            e.preventDefault();
            window.location.href = 'wishlist.html';
        });
    }

    // زر الملف الشخصي
    const profileBtn = document.getElementById('profileBtn');
    if (profileBtn) {
        profileBtn.addEventListener('click', (e) => {
            e.preventDefault();
            window.location.href = 'profile.html';
        });
    }
}

// ===============================
// وظائف التسوق والسلة
// ===============================

// إضافة منتج للسلة
function addToCart(productId, quantity = 1) {
    let cart = JSON.parse(localStorage.getItem('msb-cart') || '[]');

    const existingItem = cart.find(item => item.id === productId);
    if (existingItem) {
        existingItem.quantity += quantity;
    } else {
        cart.push({
            id: productId,
            quantity: quantity,
            addedAt: new Date().toISOString()
        });
    }

    localStorage.setItem('msb-cart', JSON.stringify(cart));
    updateCartCount();
    showNotification(t('cart.item_added', 'تم إضافة المنتج للسلة'), 'success');
}

// إضافة منتج للمفضلة
function addToWishlist(productId) {
    let wishlist = JSON.parse(localStorage.getItem('msb-wishlist') || '[]');

    if (!wishlist.includes(productId)) {
        wishlist.push(productId);
        localStorage.setItem('msb-wishlist', JSON.stringify(wishlist));
        updateWishlistCount();
        showNotification(t('wishlist.item_added', 'تم إضافة المنتج للمفضلة'), 'success');
    } else {
        showNotification(t('wishlist.item_exists', 'المنتج موجود بالفعل في المفضلة'), 'warning');
    }
}

// شراء فوري
function buyNow(productId) {
    addToCart(productId, 1);
    setTimeout(() => {
        window.location.href = 'cart.html';
    }, 500);
}

// عرض تفاصيل المنتج
function viewProductDetails(productId) {
    showNotification(t('product.viewing_details', 'عرض تفاصيل المنتج'), 'info');
    // يمكن إضافة منطق عرض التفاصيل هنا
}

// تحديث عداد السلة
function updateCartCount() {
    const cart = JSON.parse(localStorage.getItem('msb-cart') || '[]');
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);

    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        cartCount.textContent = totalItems;
        cartCount.style.display = totalItems > 0 ? 'block' : 'none';
    }
}

// تحديث عداد المفضلة
function updateWishlistCount() {
    const wishlist = JSON.parse(localStorage.getItem('msb-wishlist') || '[]');

    const wishlistCount = document.getElementById('wishlistCount');
    if (wishlistCount) {
        wishlistCount.textContent = wishlist.length;
        wishlistCount.style.display = wishlist.length > 0 ? 'block' : 'none';
    }
}

// وظيفة البحث
function performSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput && searchInput.value.trim()) {
        const query = searchInput.value.trim();
        showNotification(t('search.searching', 'جاري البحث...'), 'info');

        // توجيه لصفحة المنتجات مع البحث
        setTimeout(() => {
            window.location.href = `products.html?search=${encodeURIComponent(query)}`;
        }, 500);
    }
}

// ===============================
// المساعد الذكي العائم
// ===============================

// إنشاء المساعد الذكي
function createSmartAssistant() {
    // التحقق من وجود المساعد
    if (document.getElementById('smartAssistant')) return;

    const assistant = document.createElement('div');
    assistant.id = 'smartAssistant';
    assistant.innerHTML = `
        <!-- أيقونة المساعد العائمة -->
        <div id="assistantIcon" class="fixed bottom-6 right-6 w-16 h-16 bg-pharaoh-blue rounded-full shadow-lg cursor-pointer z-50 flex items-center justify-center text-2xl hover:scale-110 transition-all duration-300 animate-pulse">
            🤖
        </div>

        <!-- نافذة المحادثة -->
        <div id="chatWindow" class="fixed bottom-24 right-6 w-80 h-96 bg-white rounded-lg shadow-xl border-2 border-pharaoh-gold hidden z-50">
            <!-- رأس النافذة -->
            <div class="bg-pharaoh-blue text-white p-4 rounded-t-lg flex justify-between items-center">
                <div class="flex items-center space-x-2 space-x-reverse">
                    <span class="text-xl">🤖</span>
                    <span class="font-bold" data-translate="assistant.title">المساعد الذكي</span>
                </div>
                <button id="closeChatBtn" class="text-white hover:text-pharaoh-gold transition-colors">✕</button>
            </div>

            <!-- منطقة المحادثة -->
            <div id="chatMessages" class="h-64 overflow-y-auto p-4 space-y-3">
                <div class="bg-pharaoh-gold/20 p-3 rounded-lg">
                    <p class="text-sm" data-translate="assistant.welcome">مرحباً! كيف يمكنني مساعدتك اليوم؟</p>
                </div>
            </div>

            <!-- منطقة الكتابة -->
            <div class="p-4 border-t">
                <div class="flex space-x-2 space-x-reverse">
                    <input
                        type="text"
                        id="chatInput"
                        class="flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-pharaoh-blue"
                        data-translate-placeholder="assistant.type_message"
                        placeholder="اكتب رسالتك هنا..."
                    >
                    <button id="sendChatBtn" class="bg-pharaoh-blue text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors">
                        📤
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(assistant);
    initializeAssistantEvents();
}

// تهيئة أحداث المساعد
function initializeAssistantEvents() {
    const assistantIcon = document.getElementById('assistantIcon');
    const chatWindow = document.getElementById('chatWindow');
    const closeChatBtn = document.getElementById('closeChatBtn');
    const chatInput = document.getElementById('chatInput');
    const sendChatBtn = document.getElementById('sendChatBtn');

    // فتح/إغلاق نافذة المحادثة
    assistantIcon.addEventListener('click', () => {
        chatWindow.classList.toggle('hidden');
        if (!chatWindow.classList.contains('hidden')) {
            chatInput.focus();
        }
    });

    // إغلاق النافذة
    closeChatBtn.addEventListener('click', () => {
        chatWindow.classList.add('hidden');
    });

    // إرسال الرسالة
    function sendMessage() {
        const message = chatInput.value.trim();
        if (message) {
            addChatMessage(message, 'user');
            chatInput.value = '';

            // رد تلقائي من المساعد
            setTimeout(() => {
                const response = getAssistantResponse(message);
                addChatMessage(response, 'assistant');
            }, 1000);
        }
    }

    sendChatBtn.addEventListener('click', sendMessage);
    chatInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
}

// إضافة رسالة للمحادثة
function addChatMessage(message, sender) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');

    if (sender === 'user') {
        messageDiv.className = 'bg-pharaoh-blue text-white p-3 rounded-lg ml-8 text-right';
    } else {
        messageDiv.className = 'bg-gray-100 p-3 rounded-lg mr-8';
    }

    messageDiv.innerHTML = `<p class="text-sm">${message}</p>`;
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// الحصول على رد المساعد
function getAssistantResponse(message) {
    const responses = {
        'مرحبا': 'مرحباً بك في مركز MSB! كيف يمكنني مساعدتك؟',
        'السعر': 'يمكنك العثور على أسعار جميع المنتجات في صفحة المنتجات. هل تبحث عن منتج معين؟',
        'الشحن': 'نوفر خدمة الشحن لجميع أنحاء مصر. الشحن مجاني للطلبات أكثر من 500 جنيه.',
        'الدفع': 'نقبل الدفع عند الاستلام، فودافون كاش، وجميع طرق الدفع الإلكتروني.',
        'المساعدة': 'أنا هنا لمساعدتك! يمكنك سؤالي عن المنتجات، الأسعار، الشحن، أو أي شيء آخر.',
        'default': 'شكراً لك على تواصلك معنا. سيقوم فريق الدعم بالرد عليك قريباً. يمكنك أيضاً الاتصال بنا على 19999.'
    };

    // البحث عن كلمات مفتاحية
    for (const [key, response] of Object.entries(responses)) {
        if (message.includes(key)) {
            return response;
        }
    }

    return responses.default;
}

// ===============================
// التهيئة الشاملة للموقع
// ===============================

// تهيئة جميع وظائف الموقع
function initializeWebsite() {
    // تهيئة الثيم
    initializeTheme();

    // تهيئة نظام الترجمة
    initializeTranslation();

    // تهيئة التنقل
    initializeNavigation();

    // تهيئة أزرار الإجراءات
    initializeActionButtons();

    // تهيئة أزرار الهيدر
    initializeHeaderButtons();

    // تهيئة القوائم المنسدلة
    initializeDropdowns();

    // إنشاء المساعد الذكي
    createSmartAssistant();

    // تحديث العدادات
    updateCartCount();
    updateWishlistCount();

    // تطبيق الترجمة على العناصر الجديدة
    setTimeout(() => {
        applyTranslations();
    }, 100);

    console.log('✅ تم تهيئة موقع مركز MSB بنجاح');
}

// تهيئة الموقع عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeWebsite);

// إعادة تهيئة الترجمة عند تغيير المحتوى
const observer = new MutationObserver(() => {
    applyTranslations();
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});

// تصدير الوظائف للاستخدام العام
window.MSB = {
    // وظائف الترجمة
    t,
    changeLanguage,
    applyTranslations,

    // وظائف التسوق
    addToCart,
    addToWishlist,
    buyNow,
    updateCartCount,
    updateWishlistCount,

    // وظائف الثيم
    toggleTheme,
    initializeTheme,

    // وظائف عامة
    showNotification,
    performSearch,
    initializeWebsite
};

// تصدير الوظائف للاستخدام العام
window.toggleTheme = toggleTheme;
window.showNotification = showNotification;
window.addToCart = addToCart;
window.addToWishlist = addToWishlist;
window.performSearch = performSearch;
window.bookService = bookService;
window.submitServiceBooking = submitServiceBooking;
