// الوظائف المشتركة لموقع مركز MSB
// تم إنشاؤه في 2024-01-02

// وظيفة تبديل الثيم
function toggleTheme() {
    const body = document.body;
    const themeToggle = document.getElementById('themeToggle');

    if (body.classList.contains('dark')) {
        body.classList.remove('dark');
        if (themeToggle) themeToggle.textContent = '🌙';
        localStorage.setItem('theme', 'light');
        showNotification(t('msg.theme_changed') + ' - ' + t('theme.light'), 'success');
    } else {
        body.classList.add('dark');
        if (themeToggle) themeToggle.textContent = '☀️';
        localStorage.setItem('theme', 'dark');
        showNotification(t('msg.theme_changed') + ' - ' + t('theme.dark'), 'success');
    }
}

// تحميل الثيم المحفوظ
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme');
    const themeToggle = document.getElementById('themeToggle');

    if (savedTheme === 'dark') {
        document.body.classList.add('dark');
        if (themeToggle) themeToggle.textContent = '☀️';
    } else {
        if (themeToggle) themeToggle.textContent = '🌙';
    }

    // ربط زر تبديل الثيم
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }
}

// وظيفة الإشعارات المحسنة
function showNotification(message, type = 'info', duration = 3000) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full max-w-sm`;

    // تحديد الألوان والأيقونات حسب النوع
    const typeConfig = {
        success: { bg: 'bg-green-500', icon: '✅' },
        warning: { bg: 'bg-yellow-500', icon: '⚠️' },
        error: { bg: 'bg-red-500', icon: '❌' },
        info: { bg: 'bg-blue-500', icon: 'ℹ️' }
    };

    const config = typeConfig[type] || typeConfig.info;
    notification.classList.add(config.bg, 'text-white');

    notification.innerHTML = `
        <div class="flex items-center">
            <span class="mr-2 text-lg">${config.icon}</span>
            <span class="flex-1">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-200 text-xl">✕</button>
        </div>
    `;

    document.body.appendChild(notification);

    // إظهار الإشعار
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // إخفاء الإشعار تلقائياً
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, duration);

    return notification;
}

// وظيفة إدارة القوائم المنسدلة
function initializeDropdowns() {
    // قائمة اللغات
    const languageToggle = document.getElementById('languageToggle');
    const languageMenu = document.getElementById('languageMenu');
    
    if (languageToggle && languageMenu) {
        languageToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            languageMenu.classList.toggle('hidden');
            // إغلاق القوائم الأخرى
            const userMenu = document.getElementById('userMenu');
            if (userMenu) userMenu.classList.add('hidden');
        });

        // تبديل اللغات
        document.querySelectorAll('.language-option').forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const lang = this.dataset.lang;
                changeLanguage(lang);
                languageMenu.classList.add('hidden');
            });
        });
    }

    // قائمة المستخدم
    const userToggle = document.getElementById('userToggle');
    const userMenu = document.getElementById('userMenu');

    if (userToggle && userMenu) {
        userToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenu.classList.toggle('hidden');
            // إغلاق القوائم الأخرى
            if (languageMenu) languageMenu.classList.add('hidden');
        });

        // خيارات قائمة المستخدم
        document.querySelectorAll('.user-option').forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const action = this.dataset.action;
                handleUserAction(action);
                userMenu.classList.add('hidden');
            });
        });
    }

    // إغلاق القوائم عند النقر خارجها
    document.addEventListener('click', function() {
        if (languageMenu) languageMenu.classList.add('hidden');
        if (userMenu) userMenu.classList.add('hidden');
    });
}

// وظيفة التعامل مع إجراءات المستخدم
function handleUserAction(action) {
    switch(action) {
        case 'profile':
            showNotification(t('nav.account') + '...', 'info');
            setTimeout(() => {
                window.location.href = 'profile.html';
            }, 500);
            break;
        case 'orders':
            showNotification(t('nav.orders') + '...', 'info');
            setTimeout(() => {
                window.location.href = 'orders.html';
            }, 500);
            break;
        case 'wishlist':
            showNotification(t('nav.wishlist') + '...', 'info');
            setTimeout(() => {
                window.location.href = 'wishlist.html';
            }, 500);
            break;
        case 'settings':
            showNotification(t('nav.settings') + '...', 'info');
            setTimeout(() => {
                window.location.href = 'settings.html';
            }, 500);
            break;
        case 'logout':
            if (confirm(t('msg.confirm_delete'))) {
                showNotification(t('nav.logout') + '...', 'warning');
                setTimeout(() => {
                    showNotification(t('msg.success'), 'success');
                    // تنفيذ تسجيل الخروج
                }, 1000);
            }
            break;
    }
}

// وظيفة إضافة منتج للسلة
function addToCart(productId, productName) {
    // محاكاة إضافة المنتج للسلة
    showNotification(t('msg.item_added_cart') + ': ' + productName, 'success');
    
    // تحديث عداد السلة
    updateCartCounter();
}

// وظيفة إضافة منتج للمفضلة
function addToWishlist(productId, productName) {
    // محاكاة إضافة المنتج للمفضلة
    showNotification(t('msg.item_added_wishlist') + ': ' + productName, 'success');
    
    // تحديث عداد المفضلة
    updateWishlistCounter();
}

// تحديث عداد السلة
function updateCartCounter() {
    const cartCounter = document.querySelector('.cart-counter');
    if (cartCounter) {
        let count = parseInt(cartCounter.textContent) || 0;
        cartCounter.textContent = count + 1;
    }
}

// تحديث عداد المفضلة
function updateWishlistCounter() {
    const wishlistCounter = document.querySelector('.wishlist-counter');
    if (wishlistCounter) {
        let count = parseInt(wishlistCounter.textContent) || 0;
        wishlistCounter.textContent = count + 1;
    }
}

// وظيفة البحث
function initializeSearch() {
    const searchInput = document.querySelector('input[type="text"][placeholder*="بحث"], input[type="text"][placeholder*="Search"]');
    
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch(this.value);
            }
        });
        
        // تحديث placeholder حسب اللغة
        searchInput.setAttribute('data-translate', 'search.placeholder');
    }
}

// تنفيذ البحث
function performSearch(query) {
    if (query.trim()) {
        showNotification(t('search.results') + ': ' + query, 'info');
        // هنا يمكن إضافة منطق البحث الفعلي
    }
}

// وظيفة تأثيرات التمرير
function initializeScrollEffects() {
    // تأثير التمرير السلس للروابط
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // تأثير الظهور عند التمرير
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // مراقبة العناصر للتأثيرات
    document.querySelectorAll('section, .card, .product-item').forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(element);
    });
}

// وظيفة تهيئة PWA
function initializePWA() {
    // تسجيل Service Worker
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/service-worker.js')
            .then(registration => {
                console.log('Service Worker registered successfully');
            })
            .catch(error => {
                console.log('Service Worker registration failed');
            });
    }

    // زر تثبيت التطبيق
    let deferredPrompt;
    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        
        const installButton = document.createElement('button');
        installButton.textContent = '📱 ' + t('btn.install_app', 'تثبيت التطبيق');
        installButton.className = 'fixed bottom-4 left-4 bg-pharaoh-gold text-pharaoh-blue px-4 py-2 rounded-lg shadow-lg z-50';
        
        installButton.addEventListener('click', () => {
            deferredPrompt.prompt();
            deferredPrompt.userChoice.then((choiceResult) => {
                if (choiceResult.outcome === 'accepted') {
                    showNotification(t('msg.app_installed', 'تم تثبيت التطبيق'), 'success');
                }
                deferredPrompt = null;
                installButton.remove();
            });
        });
        
        document.body.appendChild(installButton);
    });
}

// وظيفة التهيئة الرئيسية
function initializeApp() {
    // تهيئة اللغة والثيم
    initializeLanguage();
    initializeTheme();
    
    // تهيئة المكونات التفاعلية
    initializeDropdowns();
    initializeSearch();
    initializeScrollEffects();
    initializePWA();
    
    // تحديث الترجمات
    updateTranslations();
    
    console.log('🎉 تم تهيئة موقع مركز MSB بنجاح');
}

// تشغيل التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeApp);

// تصدير الوظائف للاستخدام العام
window.toggleTheme = toggleTheme;
window.showNotification = showNotification;
window.addToCart = addToCart;
window.addToWishlist = addToWishlist;
window.performSearch = performSearch;
